{"files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "editor.autoIndent": "full", "editor.tabSize": 2, "editor.indentSize": 2, "files.eol": "\n", "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": false, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "html.format.wrapAttributes": "force-expand-multiline", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "svg.preview.background": "dark-transparent", "cSpell.words": ["<PERSON><PERSON>", "axios", "<PERSON><PERSON>", "eips", "favorited", "mainte", "pems", "PGHOST", "plpgsql", "psql", "regist", "SOLDOUT", "VITE", "vuetify"]}