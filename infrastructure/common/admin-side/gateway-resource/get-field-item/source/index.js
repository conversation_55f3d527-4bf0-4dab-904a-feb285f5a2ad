const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  console.log('log of event : ', e);
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  ctx.callbackWaitsForEmptyEventLoop = false;
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      const sqlParams = [tenantNo, params.field_localized_no];
      return pool
        .rlsQuery(tenantNo, Define.QUERY.GET_FIELD_ITEM_FUNCTION, sqlParams)
        .then(constants => {
          const ret = {
            data: constants[0] || {},
          };
          return Promise.resolve(ret);
        });
    })
    .then(res => {
      return Base.createSuccessResponse(cb, res);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
