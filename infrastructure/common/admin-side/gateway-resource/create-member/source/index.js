const Define = require(`${process.env.COMMON_LAYER_PATH}define`);
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body);
  console.log(`params = ${JSON.stringify(params)}`);
  console.log('create-member');
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      const country = params.member?.freeField.country;
      console.log(`country = ${country}`);
      if (country === 'JP') {
        const rules = require('./jp-validation-rules');
        return Promise.resolve(rules);
      }
      const rules = require('./other-validation-rules');
      return Promise.resolve(rules);
    })
    .then(rules => {
      console.log('VALIDATION');
      const checkData = Object.assign(
        {},
        params.member,
        params.member.freeField
      );

      return Promise.resolve()
        .then(() => {
          console.log('Check Duplicated customerCode');
          console.log('Email: ', checkData.email);

          checkData.customerCodeDuplicated = 0;
          if (checkData?.customerCode) {
            return pool
              .rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_MEMBER_BY_CUSTOMER_CODE, [
                Base.extractTenantId(e),
                checkData.customerCode,
              ])
              .then(members => {
                console.log('members: ', members);
                if (members && members.length > 0) {
                  // Customer code is duplicated
                  checkData.customerCodeDuplicated = -1;
                }
                console.log(
                  'checkData.customerCodeDuplicated: ',
                  checkData.customerCodeDuplicated
                );
                return Promise.resolve();
              });
          }
          return Promise.resolve();
        })
        .then(() => {
          console.log('Check Duplicated Email');
          console.log('Email: ', checkData.email);

          checkData.emailDuplicated = 0;
          if (checkData?.email) {
            return pool
              .rlsQuery(Base.extractTenantId(e),Define.QUERY.GET_MEMBER_BY_EMAIL, [
                Base.extractTenantId(e),
                checkData.email,
              ])
              .then(members => {
                console.log('members: ', members);
                if (members && members.length > 0) {
                  // Email is duplicated
                  checkData.emailDuplicated = -1;
                }
                console.log(
                  'checkData.emailDuplicated: ',
                  checkData.emailDuplicated
                );
                return Promise.resolve();
              });
          }
          return Promise.resolve();
        })
        .then(() => {
          const validateResult = Validator.checkRules(checkData, rules);
          console.log('validateResult: ', validateResult);
          // Validation Fail
          if (Object.keys(validateResult).length > 0) {
            return Promise.reject(
              Validator.createErrorResponse(validateResult)
            );
          }
          // Validation successful
          if (params.validation_mode === true) {
            const response = {
              status: 200,
              message: '',
            };
            return Promise.reject(response);
          }
          return Promise.resolve();
        });
    })
    .then(() => {
      console.log('CREATE MEMBER');
      const randomPassword = Common.randomString(10);
      console.log('randomPassword', randomPassword);
      return Base.hashPassword(randomPassword)
        .then(hashPassword => {
          console.log('hashPassword', hashPassword);
          const freeField = Object.assign({}, params.member.freeField);
          return pool.rlsQuery(Base.extractTenantId(e),Define.QUERY.CREATE_MEMBER_FUNCTION, [
            Base.extractTenantId(e),
            freeField,
            params.member.bidAllowFlag,
            params.member.emailDeliveryFlag,
            hashPassword,
            1, // require password change
            0, // require confirm token
            null, // token string
            null, // token expire
            Base.extractAdminNo(e),
          ]);
        })
        .then(() => {
          // Send registered information to user's email
          const language = params.member.freeField.emailLang || 'en';
          return pool.rlsQuery(Base.extractTenantId(e),
            'SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);',
            [
              Base.extractTenantId(e),
              [
                'EMAIL_TEMPORARY_PASSWORD_INFO_FOR_MEMBER',
                'EMAIL_COMMON_FOOTER',
                'EMAIL_FROM',
              ],
              language,
            ]
          );
        })
        .then(constants => {
          const mailFrom =
            constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 || null;
          const footer =
            constants.find(x => x.key_string === 'EMAIL_COMMON_FOOTER') || {};
          return Promise.all([
            Promise.resolve().then(() => {
              const mail =
                constants.find(
                  x =>
                    x.key_string === 'EMAIL_TEMPORARY_PASSWORD_INFO_FOR_MEMBER'
                ) || {};
              const title = mail.value1;
              const sender = mailFrom
                ? `"${mailFrom}"<${mail.value2}>`
                : mail.value2;
              const receivers = [params.member.freeField.email];
              const bcc = mail.value3 ? mail.value3.split(',') : [];
              const content = Common.format(mail.value4, [
                randomPassword,
                footer.value4,
              ]);

              return Common.sendMailBySES(
                title,
                content,
                sender,
                receivers,
                bcc
              );
            }),
          ]);
        });
    })
    .then(result => {
      console.log(`result = ${JSON.stringify(result)}`);
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
