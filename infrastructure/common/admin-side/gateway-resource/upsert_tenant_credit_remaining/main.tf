module "gateway-resource" {
  source = "../../../../modules/gateway-resource"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  prefix_function_name = var.prefix_function_name
  description = local.description
  lambda_layer = var.lambda_layer
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  path_module = path.module
  lambda_global_environment_variables = var.lambda_global_environment_variables
  local_environment_variables = local.environment_variables
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.parent_id
  parent_path = var.parent_path
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  resource_folder_name = basename(path.module)
  aws_api_gateway_authorizer_id = var.aws_api_gateway_authorizer_id
  authorization = "COGNITO_USER_POOLS"
}
