CREATE OR REPLACE FUNCTION public.f_get_item_detail (
    in_item_no bigint,
    in_tenant_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    lot_no bigint,
    item_no bigint,
    manage_no character varying,
    exhibition_no bigint,
    area_id character varying,
    status integer,
    quantity numeric,
    lowest_bid_price numeric,
    lowest_bid_accept_price numeric,
    lowest_bid_quantity numeric,
    lowest_bid_accept_quantity numeric,
    default_end_datetime timestamp with time zone,
    preview_start_datetime timestamp with time zone,
    preview_end_datetime timestamp with time zone,
    price_display_flag integer,
    localized_json_array json[],
    ancillary_json_array json[],
    success_company_name character varying,
    success_price numeric,
    currency_id character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 商品詳細情報を取得する
----------------------------------------------------------------------------------------------------
sold_out_display_date character varying;

BEGIN

  SELECT INTO sold_out_display_date MCL.value1
    FROM m_constant MC
    JOIN m_constant_localized MCL
      ON MCL.constant_no = MC.constant_no
  WHERE MC.tenant_no = in_tenant_no
    AND MCL.language_code = in_language_code
    AND MC.key_string = 'SOLD_OUT_DISPLAY_DATE'
    AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')));

  RETURN QUERY
  WITH Localized AS (
    SELECT L.item_no
         , array_agg(row_to_json(row(L.language_code, L.free_field))) localized_json_array
      FROM t_item_localized L
     WHERE L.item_no = in_item_no
     GROUP BY L.item_no
  ), Ancillary AS (
    SELECT I.item_no
         , AF.manage_no
         , array_agg(row_to_json(row(AF.language_code, AF.division, AF.serial_number, AF.file_path, AF.postar_file_path))) ancillary_json_array
      FROM t_item_ancillary_file AF
      LEFT JOIN t_item I
        ON AF.manage_no = I.manage_no
       AND AF.item_no = I.item_no
     WHERE I.item_no = in_item_no
       AND AF.delete_flag = 0
     GROUP BY I.item_no, AF.manage_no
  ), StockResult AS (
    SELECT I.item_no
         , SR.manage_no
         , COALESCE(((SR.member_field->>'name')::character varying), '') as success_company_name
         , SR.success_price
      FROM t_stock_result SR
      LEFT JOIN t_item I
        ON SR.manage_no = I.manage_no
       AND SR.item_no = I.item_no
     WHERE I.item_no = in_item_no
  )
  SELECT TLD.lot_no
       , I.item_no
       , I.manage_no
       , TE.exhibition_no
       , I.area_id
       , I.status
       , TEI.quantity
       , TEI.lowest_bid_price
       , TEI.lowest_bid_accept_price
       , TEI.lowest_bid_quantity
       , TEI.lowest_bid_accept_quantity
       , TEI.default_end_datetime
       , TE.preview_start_datetime
       , TE.preview_end_datetime
       , I.price_display_flag
       , L.localized_json_array
       , A.ancillary_json_array
       , SR.success_company_name
       , SR.success_price
       , TE.currency_id
    FROM t_item I
    LEFT JOIN Localized L
      ON L.item_no = I.item_no
    LEFT JOIN Ancillary A
      ON A.item_no = I.item_no
    LEFT JOIN StockResult SR
      ON SR.item_no = I.item_no
    LEFT JOIN t_lot_detail TLD
      ON TLD.item_no = I.item_no
    LEFT JOIN t_exhibition_item TEI
      ON TEI.lot_no = TLD.lot_no
    LEFT JOIN t_exhibition TE
      ON TEI.exhibition_no = TE.exhibition_no
   WHERE I.item_no = in_item_no
    --  AND TLD.lot_no IS NOT NULL　TODO:　SAASでは使うか確認します
     AND (
           I.status IN (0,1,2)
           OR (I.status = 3 AND linked_flag = 1 AND current_timestamp < (linked_datetime + CAST(sold_out_display_date || ' days' AS interval)))
         )
     AND I.tenant_no = in_tenant_no
     AND I.delete_flag = 0;

END;

$BODY$;
