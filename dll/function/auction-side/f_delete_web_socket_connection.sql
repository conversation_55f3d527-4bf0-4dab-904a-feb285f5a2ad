CREATE OR REPLACE FUNCTION public.f_delete_web_socket_connection (
    in_connection_id character varying
)
RETURNS TABLE(
    connection_id character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： WebSocket接続削除
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    DELETE FROM t_web_socket_connection TWSC
    WHERE TWSC.connection_id = in_connection_id
    RETURNING TWSC.connection_id
     ;

END;

$BODY$;
