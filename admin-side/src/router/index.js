import {h, resolveComponent} from 'vue';
import {createRouter, createWebHashHistory} from 'vue-router';
import {useAuthStore} from '@/store/auth';

import TheContainer from '@/containers/TheContainer';

const routes = [
  {
    path: '',
    name: '',
    component: TheContainer,
    redirect: '/pages/login',
    children: [
      {
        path: '/admins',
        name: '管理者',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        redirect: '/admins',
        children: [
          {
            path: '',
            name: '管理者一覧',
            component: () => import('@/views/admins/Admins.vue'),
          },
          {
            path: ':id/edit',
            name: '管理者編集',
            component: () => import('@/views/admins/AdminEdit.vue'),
          },
          {
            path: 'regist',
            name: '管理者登録',
            component: () => import('@/views/admins/AdminRegist.vue'),
          },
        ],
      },
      {
        path: '/members',
        name: '会員',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        redirect: '/members',
        children: [
          {
            path: '',
            name: '会員一覧',
            component: () => import('@/views/member/members.vue'),
          },
          {
            path: 'new',
            name: '会員登録',
            component: () => import('@/views/member/memberNew.vue'),
          },
          {
            path: ':id/edit',
            name: '会員編集',
            component: () => import('@/views/member/memberEdit.vue'),
          },
        ],
      },
      {
        path: '/exhibitions',
        name: '出展',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        redirect: '',
        children: [
          {
            path: '',
            name: '出展設定',
            component: () => import('@/views/exhibitions/_Exhibitions.vue'),
          },
          {
            path: 'edit/:itemNo',
            name: '商品編集',
            component: () =>
              import('@/views/exhibitions/create/ExhibitionCreate.vue'),
          },
          {
            path: 'create/:exhibitionNo',
            name: '商品新規追加',
            component: () =>
              import('@/views/exhibitions/create/ExhibitionCreate.vue'),
          },
        ],
      },
      {
        path: 'items',
        redirect: '/items',
        name: '自社商品管理',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        children: [
          {
            path: '',
            name: '自社商品管理一覧',
            component: () => import('@/views/items/_items.vue'),
          },
          {
            path: ':id/edit',
            name: '自社商品管理編集',
            component: () => import('@/views/items/itemEdit.vue'),
          },
          {
            path: ':id/inquiryChat',
            props: true,
            meta: {
              label: '問合せチャット',
              requiresAuth: true,
            },
            name: '問合せチャット',
            component: () => import('@/views/chat/InquiryChat.vue'),
          },
          {
            path: ':id/bidChat',
            props: true,
            meta: {
              label: '取引チャット',
              requiresAuth: true,
            },
            name: '取引チャット',
            component: () => import('@/views/chat/BidChat.vue'),
          },
        ],
      },
      {
        path: '/auctions',
        name: '入札会',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        redirect: '',
        children: [
          {
            path: '',
            name: '入札会一覧',
            component: () => import('@/views/auctions/_Auctions.vue'),
          },
          {
            path: ':id/edit',
            name: '入札会編集',
            component: () => import('@/views/auctions/AuctionEdit.vue'),
          },
          {
            path: 'register',
            name: '入札会登録',
            component: () => import('@/views/auctions/AuctionRegister.vue'),
          },
          {
            path: ':id/status',
            name: '入札状況',
            component: () => import('@/views/auctions/AuctionStatus.vue'),
          },
          {
            path: ':id/detail',
            name: '入札会詳細',
            component: () => import('@/views/auctions/AuctionDetail.vue'),
          },
        ],
      },
      {
        path: '/dashboard',
        name: 'ダッシュボード',
        component: () => import('@/views/dashboard/Dashboard.vue'),
        redirect: '',
      },
      {
        path: '/notices',
        name: 'お知らせ',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        redirect: '',
        children: [
          {
            path: '',
            name: 'お知らせ一覧',
            component: () => import('@/views/notices/noticeList.vue'),
          },
          {
            path: 'notice/:id',
            name: 'お知らせ編集',
            component: () => import('@/views/notices/notice.vue'),
          },
          {
            path: 'new',
            name: 'お知らせ登録',
            component: () => import('@/views/notices/notice.vue'),
          },
        ],
      },
      {
        path: '/noticeMails',
        name: 'お知らせメール',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        redirect: '',
        children: [
          {
            path: '',
            name: 'お知らせメール一覧',
            component: () => import('@/views/notice-mail/noticeMailList.vue'),
          },
          {
            path: 'notice-mail/:id',
            name: 'お知らせメール編集',
            component: () => import('@/views/notice-mail/noticeMail.vue'),
          },
          {
            path: 'new',
            name: 'お知らせメール登録',
            component: () => import('@/views/notice-mail/noticeMail.vue'),
          },
        ],
      },
      {
        path: '/inquiries',
        name: 'お問い合わせ',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        redirect: '',
        children: [
          {
            path: '',
            name: 'お問い合わせダウンロード',
            component: () => import('@/views/inquiries/Inquiries.vue'),
          },
        ],
      },
      {
        path: '/bounces',
        name: '不達メール',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        redirect: '',
        children: [
          {
            path: '',
            name: '不達メール一覧',
            component: () => import('@/views/bounces/Bounces.vue'),
          },
        ],
      },
      {
        path: '/infoMail',
        name: '入札会お知らせメール',
        component: () => import('@/views/auctions/AuctionMailInfo.vue'),
        redirect: '',
      },
      {
        path: '/constants',
        name: '定数',
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        redirect: '',
        children: [
          {
            path: '',
            name: '定数一覧',
            component: () => import('@/views/constants/Constants.vue'),
          },
          {
            path: '/constant/:id',
            name: '定数編集',
            component: () => import('@/views/constants/Constant.vue'),
          },
          {
            path: '/constant/regist',
            name: '定数登録',
            component: () => import('@/views/constants/Constant.vue'),
          },
        ],
      },
      {
        path: 'batchResult',
        meta: {
          label: 'バッチ処理結果',
        },
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        children: [
          {
            path: '',
            props: true,
            name: '結果一覧',
            component: () => import('@/views/batchResult/BatchResult.vue'),
          },
          {
            path: ':id/detail',
            props: true,
            meta: {
              label: 'バッチ結果詳細',
            },
            name: 'バッチ結果詳細',
            component: () =>
              import('@/views/batchResult/BatchResultDetail.vue'),
          },
        ],
      },
      {
        path: 'tenant',
        meta: {
          label: '各種設定',
        },
        component: {
          render() {
            return h(resolveComponent('router-view'));
          },
        },
        children: [
          {
            path: 'tenant-setting',
            name: 'テナント設定',
            component: () => import('@/views/tenant/TenantSetting.vue'),
          },
          {
            path: 'product-info',
            name: '項目設定管理',
            component: () => import('@/views/tenant/ProductInfo.vue'),
          },
          {
            path: 'display-item',
            name: '項目表示設定管理',
            component: () => import('@/views/tenant/DisplayItem.vue'),
          },
          {
            path: 'product-info/:id/new',
            name: '新規項目追加',
            component: () => import('@/components/tenant/product/NewItem.vue'),
          },
          {
            path: 'product-info/:id/edit',
            name: '項目編集',
            component: () => import('@/components/tenant/product/NewItem.vue'),
          },
          {
            path: 'permission',
            name: '権限設定管理',
            component: () => import('@/views/tenant/Permission.vue'),
          },
          {
            path: 'options',
            name: 'オプション管理',
            component: () => import('@/views/tenant/Options.vue'),
          },
          {
            path: 'ext-link',
            name: '外部連携管理',
            component: () => import('@/views/tenant/ExtLink.vue'),
          },
          {
            path: 'item-mapping',
            name: '項目マッピング管理',
            component: () => import('@/views/tenant/ItemMapping.vue'),
          },
          {
            path: 'postage',
            name: '送料設定',
            component: () => import('@/views/tenant/Postage.vue'),
          },
          {
            path: 'bid-limit',
            name: '入札上限額設定',
            component: () => import('@/views/tenant/BidLimit.vue'),
          },
          {
            path: 'static-page',
            name: '静的ページ設定',
            component: {
              render() {
                return h(resolveComponent('router-view'));
              },
            },
            redirect: '',
            children: [
              {
                path: '',
                name: '静的ページ一覧',
                component: () => import('@/views/tenant/StaticPage.vue'),
              },
              {
                path: '/tenant/static-page/:id',
                name: '静的ページ編集',
                component: () =>
                  import('@/components/tenant/staticPage/StaticPageDetail.vue'),
              },
              {
                path: 'new',
                name: '静的ページ登録',
                component: () =>
                  import('@/components/tenant/staticPage/StaticPageDetail.vue'),
              },
            ],
          },
        ],
      },
    ],
  },
  // TODO: remove this
  {
    path: '/otp',
    name: 'OTP',
    component: () => import('@/views/otp/OtpPage.vue'),
    redirect: '',
  },
  {
    path: '/pages',
    redirect: '/pages/404',
    name: 'Pages',
    component: {
      render() {
        return h(resolveComponent('router-view'));
      },
    },
    children: [
      {
        path: '404',
        name: 'Page404',
        component: () => import('@/views/pages/Page404.vue'),
      },
      {
        path: '500',
        name: 'Page500',
        component: () => import('@/views/pages/Page500.vue'),
      },
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/pages/LoginPage.vue'),
      },
      {
        path: 'login-mfa',
        name: 'LoginMFA',
        component: () => import('@/components/login-mfa/_LoginMFAPage.vue'),
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/components/register/Register.vue'),
      },
      {
        path: 'get-public-admin-page',
        name: 'GetPublicAdminPage',
        component: () => import('@/views/pages/GetPublicAdminPage.vue'),
      },
    ],
  },
];

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior() {
    // Always scroll to top
    return {top: 0};
  },
});

router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();
  // Try to fetch the user session if it's not already loaded
  if (authStore.user === null) {
    await authStore.fetchAuthSession();
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const isAuthenticated = authStore.isAuthenticated;

  if (requiresAuth && !isAuthenticated) {
    // Redirect to login page if auth is required and user is not authenticated
    next({name: 'Login'});
  } else if (to.name === 'Login' && isAuthenticated) {
    // Redirect to dashboard if user is authenticated and tries to access login page
    next({name: 'ダッシュボード'});
  } else {
    // Otherwise, proceed as normal
    next();
  }
});

/**
 * S3へデプロイ後に動的にインポートされたモジュールが取得できなかった場合のエラーハンドリング。
 * onErrorフックを使用してエラーが発生した場合にルートパス（`/`）に遷移
 * localhost環境では強制リロードを行わず、本番環境でのみ実行する
 * 参照: https://github.com/vitejs/vite/issues/11804#issuecomment-1406182566
 */
router.onError(error => {
  console.log('router error:', error);
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    console.warn(`${error.message}, force reload.`);

    // 開発環境では強制リロードを行わない（Viteの環境変数を使用）
    const isDevelopment = import.meta.env.DEV;
    if (!isDevelopment) {
      window.location.href = '/';
    } else {
      console.warn('Skipping force reload in development environment');
    }
  }
});

export default router;
