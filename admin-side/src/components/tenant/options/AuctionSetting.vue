<template>
  <div>
    <CRow>
      <CCol
        sm="4"
        class="p-3 border d-flex justify-content-center align-items-center"
        ><h5>オプション</h5></CCol
      >
      <CCol
        sm="4"
        class="p-3 border d-flex justify-content-center align-items-center"
        ><h5>競り上げ</h5></CCol
      >
      <CCol
        sm="4"
        class="p-3 border d-flex justify-content-center align-items-center"
        ><h5>封印</h5></CCol
      >
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">参加制限設定</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_seri" checked
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_fuin" checked
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">最低落札価格</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_seri" checked
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_fuin" checked
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">入札制限</CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="bid_limit_seri"
          :options="bidLimitOptions"
          v-model="option.bid_limit_seri"
        />
      </CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="bid_limit_fuin"
          :options="bidLimitOptions"
          v-model="option.bid_limit_fuin"
        />
      </CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">即決</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_seri" checked
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_fuin"
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">入札取消</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_seri"
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_fuin" checked
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">延長</CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_seri" checked
      /></CCol>
      <CCol sm="4" class="p-3 border"
        ><CFormCheck name="sankaseigen_fuin"
      /></CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">落札価格</CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="bid_success_price_seri"
          :options="bidSuccessPriceOptions"
          v-model="option.bid_success_price_seri"
        />
      </CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="bid_success_price_fuin"
          :options="bidSuccessPriceOptions"
          v-model="option.bid_success_price_fuin"
        />
      </CCol>
    </CRow>
    <CRow>
      <CCol sm="4" class="p-3 border">落札者決定</CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="winner_select_seri"
          :options="winnerSelectOptions"
          v-model="option.winner_select_seri"
        />
      </CCol>
      <CCol sm="4" class="p-3 border">
        <CFormSelect
          name="winner_select_fuin"
          :options="winnerSelectOptions"
          v-model="option.winner_select_fuin"
        />
      </CCol>
    </CRow>
  </div>
</template>
<script setup>
  import {ref} from 'vue';
  const option = ref({
    bid_limit_seri: 1,
    bid_limit_fuin: 2,
    bid_success_price_seri: 2,
    bid_success_price_fuin: 1,
    winner_select_seri: 1,
    winner_select_fuin: 1,
  });
  const bidLimitOptions = [
    {value: 1, label: '現在価格より高い'},
    {value: 2, label: '自由'},
  ];
  const bidSuccessPriceOptions = [
    {value: 1, label: 'ファーストプライス'},
    {value: 2, label: 'セカンドプライス'},
  ];
  const winnerSelectOptions = [
    {value: 1, label: '自動'},
    {value: 2, label: '手動'},
  ];
</script>
