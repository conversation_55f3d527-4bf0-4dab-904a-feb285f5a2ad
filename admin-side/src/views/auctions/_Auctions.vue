<template>
  <div class="mb-3">
    <CCard>
      <CCardHeader>
        <strong>検索条件</strong>
      </CCardHeader>
      <CCardBody>
        <CForm onsubmit="return false;">
          <CRow class="mb-3">
            <CCol sm="2"> 入札会名 </CCol>
            <CCol sm="4">
              <CFormInput
                name="exhibition_name"
                v-model="searchCondition.exhibition_name"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2">
              <label>下見開始日時</label>
            </CCol>
            <CCol sm="auto" class="pr-2">
              <CFormInput
                type="date"
                :ref="'preview_from_date'"
                horizontal
                v-model="searchCondition.preview_start_datetime_from"
                :invalid="!dateValidate.preview_start_datetime_from"
                @change="
                  e => {
                    dateValidate.preview_start_datetime_from =
                      e.target.validity.valid;
                  }
                "
              />
            </CCol>
            <div
              style="
                flex: 0;
                padding-right: 0;
                padding-left: 0;
                margin-bottom: 0;
                transform: translateY(7px);
              "
            >
              <label>～</label>
            </div>
            <CCol sm="auto" class="pr-2">
              <CFormInput
                type="date"
                :ref="'preview_to_date'"
                horizontal
                v-model="searchCondition.preview_start_datetime_to"
                :invalid="!dateValidate.preview_start_datetime_to"
                @change="
                  e => {
                    dateValidate.preview_start_datetime_to =
                      e.target.validity.valid;
                  }
                "
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2">
              <label>入札開始日時</label>
            </CCol>
            <CCol sm="auto" class="pr-2">
              <CFormInput
                type="date"
                :ref="'bid_from_date'"
                horizontal
                v-model="searchCondition.start_datetime_from"
                :invalid="!dateValidate.start_datetime_from"
                @change="
                  e => {
                    dateValidate.start_datetime_from = e.target.validity.valid;
                  }
                "
              />
            </CCol>
            <div
              style="
                flex: 0;
                padding-right: 0;
                padding-left: 0;
                margin-bottom: 0;
                transform: translateY(7px);
              "
            >
              <label>～</label>
            </div>
            <CCol sm="auto" class="pr-2">
              <CFormInput
                type="date"
                :ref="'bid_to_date'"
                horizontal
                v-model="searchCondition.start_datetime_to"
                :invalid="!dateValidate.start_datetime_to"
                @change="
                  e => {
                    console.log(e.target.validity.valid);
                    dateValidate.start_datetime_to = e.target.validity.valid;
                  }
                "
              />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>

      <CCardFooter>
        <CRow class="align-items-center">
          <CCol xs="5" class="mb-3 mb-xl-0 text-right"></CCol>
          <CCol xs="2" class="mb-3 mb-xl-0 text-right">
            <div class="d-grid gap-2">
              <CButton size="sm" color="info" @click="search" block>
                検索
              </CButton>
            </div>
          </CCol>
          <CCol xs="1" class="mb-3 mb-xl-0 text-right"></CCol>
          <CCol xs="2" class="mb-3 mb-xl-0 text-right"></CCol>
          <CCol xs="2" class="mb-3 mb-xl-0 text-right">
            <div class="d-grid gap-2">
              <CButton
                size="sm"
                color="primary"
                @click="registExhibition"
                block
                :disabled="isReadOnly"
                >新規登録</CButton
              >
            </div>
          </CCol>
        </CRow>
      </CCardFooter>
    </CCard>
    <CRow class="mt-3">
      <CCol sm="12">
        <AuctionTable
          name="exhibitionList"
          :items="exhibitionList"
          :totalCount="totalCount"
          :currentCount="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :position="position"
          :itemsSorter="itemsSorter"
          :roleId="roleId"
          caption="入札会一覧"
          @page-change="pageChange"
          @get-position="getPosition"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
        />
      </CCol>
    </CRow>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModal"
      @close="
        () => {
          errorModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle> エラー確認 </CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-for="text in errorMsg" :key="text">{{ text }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="(errorModal = false), (errorMsg = ''), (errorStatus = false)"
          color="dark"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {useCommonStore} from '@/store/common';
  import {defineProps, onBeforeMount, onMounted, ref, watch} from 'vue';
  import {useRoute, useRouter} from 'vue-router';
  import AuctionTable from './AuctionTable.vue';
  import {useAuthStore} from '@/store/auth';

  const props = defineProps({
    backFlag: {
      type: Boolean,
      require: false,
    },
  });

  const store = useCommonStore();
  const router = useRouter();
  const route = useRoute();
  const {user, isReadOnly} = useAuthStore();

  const loading = ref(true);
  const errorModal = ref(false);

  // Screen params
  const exhibitionList = ref([]);
  const constantList = ref([]);
  const searchCondition = ref({
    exhibition_name: null,
    preview_start_datetime_from: null,
    preview_start_datetime_to: null,
    start_datetime_from: null,
    start_datetime_to: null,
  });
  const activePage = ref(1);
  const itemsPerPage = ref(10);
  const pages = ref(1);
  const itemsSorter = ref({asc: false, column: 'start_datetime'});
  const previewFromDateFlag = ref(true);
  const previewToDateFlag = ref(true);
  const bidFromDateFlag = ref(true);
  const bidToDateFlag = ref(true);
  const errorMsg = ref([]);
  const roleId = ref(Number(user.role_id));

  // Counting
  const currentCount = ref('0');
  const totalCount = ref('0');
  const btnClicked = ref(false);
  const prevRoute = ref(null);
  const position = ref(0);

  // Date validation
  const dateValidate = ref({
    preview_start_datetime_from: true,
    preview_start_datetime_to: true,
    start_datetime_from: true,
    start_datetime_to: true,
  });

  onBeforeMount(() => {
    prevRoute.value = router.options.history?.state?.back;
    // 登録・編集画面からの遷移時のみ検索条件を取得
    if (
      prevRoute.value &&
      (prevRoute.value.name === '入札会編集' ||
        prevRoute.value.name === '入札会登録' ||
        prevRoute.value.name === '入札会詳細' ||
        prevRoute.value.name === '入札会お知らせメール' ||
        prevRoute.value.name === '入札状況')
    ) {
      vm.searchCondition = store.exhibitionsSearchCondition;
      if (
        prevRoute.value.name === '入札会編集' ||
        prevRoute.value.name === '入札状況'
      ) {
        position.value = store.position;
      }
    } else {
      // それ以外は初期化
      store.set([
        'exhibitionsSearchCondition',
        {
          exhibition_name: null,
          preview_start_datetime_from: null,
          preview_start_datetime_to: null,
          start_datetime_from: null,
          start_datetime_to: null,
        },
      ]);
      store.set(['activePage', 1]);
      store.set(['position', 0]);
      store.set(['itemsPerPage', 10]);
      store.set(['itemsSorter', {asc: false, column: 'start_datetime'}]);
    }
  });

  watch(
    () => route.query?.page,
    newVal => {
      if (newVal) {
        activePage.value = Number(newVal);
      }
    }
  );

  watch(
    () => searchCondition.value,
    newVal => {
      store.set(['exhibitionsSearchCondition', newVal]);
    }
  );

  watch(
    () => position.value,
    newVal => {
      store.set(['position', newVal]);
    }
  );
  watch(
    () => itemsPerPage.value,
    newVal => {
      if (exhibitionList.value.length > newVal) {
        pages.value =
          Number.parseInt(exhibitionList.value.length / newVal, 10) +
          (exhibitionList.value.length % newVal > 0 ? 1 : 0);
      } else {
        pages.value = 1;
      }
    }
  );

  const pageChange = val => {
    store.set(['activePage', val]);
    router.push({query: {page: val}}).catch(() => {});
  };

  const paginationChange = val => {
    itemsPerPage.value = val;
    store.set(['itemsPerPage', val]);
  };

  const getPosition = val => {
    position.value = Number.parseInt(val, 10);
    store.set(['position', position.value]);
  };

  const sorterChange = val => {
    itemsSorter.value = val;
    store.set(['itemsSorter', val]);
    pageChange(1);
  };

  const getExhibitions = () => {
    loading.value = true;
    searchCondition.value.previewFromDateFlag =
      dateValidate.value.preview_start_datetime_from;
    searchCondition.value.previewToDateFlag =
      dateValidate.value.preview_start_datetime_to;
    searchCondition.value.bidFromDateFlag =
      dateValidate.value.start_datetime_from;
    searchCondition.value.bidToDateFlag = dateValidate.value.start_datetime_to;
    console.log(searchCondition.value);

    currentCount.value = '0';
    totalCount.value = '0';

    return Methods.apiExecute('search-exhibitions', searchCondition.value).then(
      response => {
        if (response.status === 200) {
          loading.value = false;

          totalCount.value = response.data
            ? Base.number2string(response.data.total_count || 0)
            : '0';
          currentCount.value = response.data
            ? Base.number2string(response.data.current_count || 0)
            : '0';

          const now = new Date();
          for (const row of response.data.data) {
            row.auctionClassification = constantList.value.find(
              x =>
                x.key_string === 'AUCTION_CLASSIFICATION' &&
                x.value1 ==
                  row.exhibition_classification_info.auctionClassification
            ).value2;

            if (row.localized_json_array) {
              for (const key of Object.keys(row.localized_json_array)) {
                if (row.localized_json_array[key].f1 === user.language_code) {
                  row.exhibition_name = row.localized_json_array[key].f2;
                }
              }
            } else {
              row.exhibition_name = '未設定';
            }

            const endDateTime =
              row.preview_end_datetime ??
              row.max_extend_datetime ??
              row.end_datetime;
            row.endFlag = Date.parse(endDateTime) - Date.parse(now) < 0;
          }

          exhibitionList.value = response.data.data;
          console.log('exhibitionList: ', exhibitionList);
          return Promise.resolve();
        }
        return Promise.resolve(null);
      }
    );
  };

  const getConstants = () => {
    loading.value = true;
    return Methods.apiExecute('get-constants-by-keys', {
      key_strings: ['AUCTION_CLASSIFICATION'],
    }).then(response => {
      constantList.value = response.data || null;
      return Promise.resolve(null);
    });
  };

  const registExhibition = () => {
    if (btnClicked.value) {
      return;
    }
    btnClicked.value = true;
    router.push({name: '入札会登録'});
  };

  const search = () => {
    getExhibitions()
      .then(() => {
        if (errorMsg.value.length === 0) {
          pages.value =
            Number.parseInt(
              exhibitionList.value.length / itemsPerPage.value,
              10
            ) + (exhibitionList.value.length % itemsPerPage.value > 0 ? 1 : 0);
          sorterChange({asc: false, column: 'start_datetime'});
        } else {
          loading.value = false;
        }
      })
      .catch(error => {
        console.log(error);
        loading.value = false;
        errorModal.value = true;
        errorMsg.value = Methods.parseHtmlResponseError(router, error);
      });
  };

  onMounted(() => {
    getConstants()
      .then(() => {
        getExhibitions()
          .then(() => {
            // 登録・編集画面からの遷移時は閲覧してたページに戻す
            if (
              prevRoute.value &&
              (prevRoute.value.name === '入札会編集' ||
                prevRoute.value.name === '入札会登録' ||
                prevRoute.value.name === '入札会詳細' ||
                prevRoute.value.name === '入札状況' ||
                prevRoute.value.name === '入札会お知らせメール')
            ) {
              if (
                props.backFlag.value &&
                prevRoute.value &&
                (prevRoute.value.name === '入札会編集' ||
                  prevRoute.value.name === '入札状況')
              ) {
                position.value = store.position;
              }
              itemsPerPage.value = store.itemsPerPage;
              itemsSorter.value = store.itemsSorter;
            }
            pages.value =
              Number.parseInt(
                exhibitionList.value.length / itemsPerPage.value,
                10
              ) +
              (exhibitionList.value.length % itemsPerPage.value > 0 ? 1 : 0);
            activePage.value =
              store.activePage > pages.value
                ? Number(pages.value)
                : store.activePage;
            router.push({query: {page: activePage.value}}).catch(() => {});
          })
          .catch(error => {
            console.log(error);
            loading.value = false;
            errorModal.value = true;
            errorMsg.value = Methods.parseHtmlResponseError(router, error);
          });

        setTimeout(() => {
          if (
            position.value &&
            document.getElementById(`item_${position.value}`) &&
            props.backFlag
          ) {
            document.getElementById(`item_${position.value}`).scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'center',
            });
          }
        }, 1000);
      })
      .catch(error => {
        console.log(error);
        Methods.parseHtmlResponseError(router, error);
      });
  });
</script>
