<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardHeader>
        <strong>権限設定管理</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> 対象 </CCol>
            <CCol sm="5">
              <CFormSelect
                name="resource_type"
                :options="permissionObjects"
                v-model="search_condition.object"
              />
            </CCol>
          </CRow>
        </CForm>
        <CRow class="align-items-center mt-4">
          <CCol sm="5"></CCol>
          <CCol sm="2" class="mb-xl-0 text-right d-grid">
            <CButton size="sm" color="info" @click="search" block>検索</CButton>
          </CCol>
          <CCol sm="1" />
          <CCol sm="2"></CCol>
          <CCol sm="2"></CCol>
        </CRow>
      </CCardBody>
    </CCard>
    <CRow>
      <CCol sm="12">
        <ProductInfoTable
          name="resourceList"
          :items="resourceList"
          :total_count="totalCount"
          :current_count="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :activePage="activePage"
          :itemsPerPage="itemsPerPage"
          :pages="pages"
          :itemsSorter="itemsSorter"
          caption="項目一覧"
          @page-change="pageChange"
          @pagination-change="paginationChange"
          @sorter-change="sorterChange"
          @update-permission="updatePermission"
        />
      </CCol>
    </CRow>

    <CModal
      title="確認"
      color="primary"
      v-model:show="isErrorDialog"
      :closeOnBackdrop="false"
    >
      <div v-if="validateResult">
        <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
      </div>
      <template #header>
        <h5 class="modal-title">確認</h5>
        <CCloseButton @click="isErrorDialog = false" />
      </template>
      <template #footer>
        <CButton @click="isErrorDialog = false" color="dark">閉じる</CButton>
      </template>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import {ScaleLoader} from '@/components/Table';
  import {useCommonStore} from '@/store/common';
  import ProductInfoTable from '../../components/tenant/permission/PermissionTable.vue';

  export default {
    name: 'ProductInfo',
    components: {
      ProductInfoTable,
      ScaleLoader,
    },
    setup() {
      const store = useCommonStore();
      return {store};
    },
    data() {
      return {
        loading: true,
        // 検索条件
        permissionObjects: [],

        // Screen params
        resourceList: [],
        search_condition: {
          object: '1',
        },
        activePage: 1,
        itemsPerPage: 10,
        pages: 1,
        itemsSorter: {},

        // Counting
        current_count: 0,
        total_count: 0,

        // CSV用
        validateResult: [],

        // Error dialog
        isErrorDialog: false,
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;

        // 初期化
        vm.store.set(['resourceSearchCondition', {object: '1'}]);
        vm.store.set(['activePage', 1]);
        vm.store.set(['position', 0]);
        vm.store.set(['itemsPerPage', 10]);
        vm.store.set(['itemsSorter', {asc: true, column: 'resource_type'}]);
      });
    },
    mounted() {
      this.getConstants()
        .then(() => {
          this.getResourceList()
            .then(postage => {
              this.resourceList = postage;

              this.itemsPerPage = this.store.itemsPerPage;

              this.pages =
                parseInt(this.resourceList.length / this.itemsPerPage, 10) +
                (this.resourceList.length % this.itemsPerPage > 0 ? 1 : 0);
              this.activePage =
                this.store.activePage > this.pages
                  ? Number(this.pages)
                  : this.store.activePage;
              this.$router
                .push({query: {page: this.activePage}})
                .catch(() => {});
            })
            .catch(error => {
              console.log(error);
              this.loading = false;
              this.errorModal = true;
              this.errorMsg = Methods.parseHtmlResponseError(
                this.$router,
                error
              );
            });
          const self = this;
          setTimeout(() => {
            if (
              self.position &&
              document.getElementById(`item_${self.position}`)
            ) {
              document.getElementById(`item_${self.position}`).scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center',
              });
            }
          }, 1000);
        })
        .catch(error => {
          console.log(error);
          Methods.parseHtmlResponseError(this.$router, error);
        });
    },
    watch: {
      $route: {
        immediate: true,
        handler(route) {
          if (route.query && route.query.page) {
            this.activePage = Number(route.query.page);
          }
        },
      },
      search_condition: {
        handler(newVal) {
          this.store.set(['resourceSearchCondition', newVal]);
        },
        deep: true,
        immediate: false,
      },
      itemsPerPage(newVal) {
        if (this.resourceList.length > newVal) {
          this.pages =
            parseInt(this.resourceList.length / newVal, 10) +
            (this.resourceList.length % newVal > 0 ? 1 : 0);
        } else {
          this.pages = 1;
        }
      },
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
      currentCount() {
        return Base.number2string(this.current_count);
      },
    },
    methods: {
      getConstants() {
        this.loading = true;
        return Methods.apiExecute('get-constants-by-keys', {
          key_strings: ['PERMISSION_OBJECT'],
        }).then(response => {
          if (response.status === 200) {
            this.constantList = response.data;
            this.permissionObjects.push({label: '', value: null});
            for (const i of response.data.filter(
              x => x.key_string === 'PERMISSION_OBJECT'
            )) {
              this.permissionObjects.push({label: i.value2, value: i.value1});
            }
            return Promise.resolve();
          }
          return Promise.resolve();
        });
      },
      search() {
        this.isErrorDialog = false;
        if (
          this.search_condition.resource_type === null &&
          this.search_condition.start_area === null &&
          this.search_condition.end_area === null
        ) {
          this.validateResult = ['1つ以上の条件を選択してください。'];
          this.isErrorDialog = true;
          return;
        }
        this.getResourceList()
          .then(postage => {
            this.resourceList = postage;

            this.pages =
              parseInt(this.resourceList.length / this.itemsPerPage, 10) +
              (this.resourceList.length % this.itemsPerPage > 0 ? 1 : 0);
            this.sorterChange({asc: true, column: 'resource_type'});
          })
          .catch(error => {
            this.loading = false;
            Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getResourceList() {
        this.total_count = 0;
        this.current_count = 0;

        console.log('検索条件:', this.search_condition);

        // Request to server
        return Methods.apiExecute(
          'get-permission-list',
          this.search_condition
        ).then(response => {
          if (response.status === 200) {
            this.loading = false;
            const resourceList = response.data.data;
            this.total_count = response.data
              ? response.data.total_count || 0
              : 0;
            this.current_count = response.data
              ? response.data.current_count || 0
              : 0;
            return Promise.resolve(resourceList);
          }
          return Promise.resolve(null);
        });
      },
      pageChange(val) {
        this.store.set(['activePage', val]);
        this.$router.push({query: {page: val}}).catch(() => {});
      },
      paginationChange(val) {
        this.itemsPerPage = val;
        this.store.set(['itemsPerPage', val]);
      },
      sorterChange(val) {
        this.itemsSorter = val;
        this.store.set(['itemsSorter', val]);
        this.pageChange(1);
      },
      updatePermission() {
        console.log('update');
      },
    },
  };
</script>
