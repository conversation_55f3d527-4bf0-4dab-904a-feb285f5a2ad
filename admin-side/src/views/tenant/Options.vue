<script setup>
  import {CButton} from '@coreui/vue';
  import {ref} from 'vue';
  import AuctionSetting from '../../components/tenant/options/AuctionSetting.vue';
  import FunctionSetting from '../../components/tenant/options/FunctionSetting.vue';
  const tabPanePillsActiveKey = ref(1);
</script>
<template>
  <CCard class="text-center mb-3">
    <CCardHeader class="d-flex justify-content-between">
      <CNav variant="tabs" class="card-header-tabs">
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 1"
            @click="
              () => {
                tabPanePillsActiveKey = 1;
              }
            "
          >
            機能
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 2"
            @click="
              () => {
                tabPanePillsActiveKey = 2;
              }
            "
          >
            入札仕様
          </CNavLink>
        </CNavItem>
      </CNav>
      <CButton color="primary" class="float-right" @click="() => {}">
        保存
      </CButton>
    </CCardHeader>
    <CCardBody>
      <CTabContent>
        <CTabPane
          role="tabpanel"
          aria-labelledby="home-tab"
          :visible="tabPanePillsActiveKey === 1"
        >
          <FunctionSetting />
        </CTabPane>
        <CTabPane
          role="tabpanel"
          aria-labelledby="profile-tab"
          :visible="tabPanePillsActiveKey === 2"
        >
          <AuctionSetting />
        </CTabPane>
      </CTabContent>
    </CCardBody>
  </CCard>
</template>
