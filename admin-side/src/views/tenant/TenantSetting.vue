<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>テナント設定</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> テナント名 </CCol>
            <CCol sm="10">
              <CFormInput name="tenant_name" v-model="tenant.tenant_name" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 運営者名 </CCol>
            <CCol sm="10">
              <CFormInput name="operator_name" v-model="tenant.operator_name" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 連絡先 </CCol>
            <CCol sm="10">
              <CFormInput name="contact" v-model="tenant.contact" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 認証 </CCol>
            <CCol sm="10">
              <CFormCheck
                id="mfa_required"
                label="会員の２段階認証を必須とする"
                v-model="tenant.mfa_required"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 通貨 </CCol>
            <CCol sm="2">
              <CFormSelect
                name="currency"
                :options="currencyOptions"
                v-model="tenant.currency"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2"> 言語 </CCol>
            <CCol sm="10">
              <CFormCheck
                v-for="lang in languageOptions"
                :key="lang.value"
                inline
                :id="lang.value"
                :value="lang.value"
                :label="lang.label"
                v-model="tenant.language"
              />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
      <CRow class="mb-3">
        <CCol sm="5" />
        <CCol sm="2" class="d-grid">
          <CButton color="primary">更新</CButton>
        </CCol>
        <CCol sm="5" />
      </CRow>
    </CCard>
  </div>
</template>

<script setup>
  import {ref} from 'vue';

  const tenant = ref({
    tenant_name: '',
    operator_name: '',
    contact: '',
    mfa_required: '0',
    currency: null,
    language: [],
  });

  const currencyOptions = [
    {value: 'JPY', label: 'JPY'},
    {value: 'USD', label: 'USD'},
    {value: 'EUR', label: 'EUR'},
  ];
  const languageOptions = [
    {value: 'ja', label: '日本語'},
    {value: 'en', label: '英語'},
    {value: 'fr', label: 'フランス語'},
    {value: 'de', label: 'ドイツ語'},
    {value: 'es', label: 'スペイン語'},
  ];
</script>
