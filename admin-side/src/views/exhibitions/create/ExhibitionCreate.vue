<template>
  <div class="mb-3">
    <CCard>
      <CCardHeader>
        <strong>商品情報</strong>
      </CCardHeader>
      <CCardBody>
        <div v-if="loading" class="my-3 text-center">
          <CSpinner />
        </div>
        <CForm onsubmit="return false;" v-show="!loading">
          <CRow class="font-xs mb-3">
            <CCol v-if="true" sm="4">
              <ImageUpload
                :origImages="stockData.picturePath"
                @onChange="onImageChange"
                @onUploadStart="onUploadStart"
                @onUploadFinish="onUploadFinish"
              />
            </CCol>
            <CCol sm="8">
              <CRow class="mb-3">
                <CCol sm="3" class="fw-bolder d-flex align-items-start">
                  <label>商品ID</label>
                  <CBadge color="danger" class="ms-auto">必須</CBadge>
                </CCol>
                <CCol sm="4">
                  <CFormInput
                    name="manage_no"
                    v-model="stockData.manage_no"
                    :disabled="itemNo ? true : false"
                  />
                </CCol>
              </CRow>
              <CRow class="mb-3">
                <CCol sm="3" class="fw-bolder d-flex align-items-start">
                  <label>カテゴリー</label>
                  <CBadge color="danger" class="ms-auto">必須</CBadge>
                </CCol>
                <CCol sm="4">
                  <CFormSelect
                    name="category"
                    :options="optionsCategory"
                    v-model="stockData.free_field.category"
                  />
                </CCol>
              </CRow>
              <CRow class="mb-3">
                <CCol sm="3" class="fw-bolder d-flex align-items-start">
                  <label>メーカー</label>
                  <CBadge color="danger" class="ms-auto">必須</CBadge>
                </CCol>
                <CCol sm="4">
                  <CFormInput
                    name="maker"
                    v-model="stockData.free_field.maker"
                  />
                </CCol>
              </CRow>
            </CCol>
          </CRow>

          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>商品名</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="10">
              <CFormInput
                name="product_name"
                v-model="stockData.free_field.product_name"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>SIM</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="2">
              <CFormInput name="sim" v-model="stockData.free_field.sim" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>グレード</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="2">
              <CFormInput name="rank" v-model="stockData.free_field.rank" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>容量</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="2">
              <CFormInput
                name="capacity"
                v-model="stockData.free_field.capacity"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>色</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="2">
              <CFormInput name="color" v-model="stockData.free_field.color" />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>最低入札価格</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="2">
              <CFormInput
                class="text-end"
                maxLength="8"
                name="lowest_bid_price"
                v-on:keypress="isNumber"
                v-on:focusin="
                  () => {
                    lowest_bid_price = lowest_bid_price
                      ? Base.localeString2Number(lowest_bid_price)
                      : '';
                  }
                "
                v-on:focusout="
                  event => {
                    lowest_bid_price = priceLocaleString(event);
                  }
                "
                v-model="lowest_bid_price"
              />
            </CCol>
            <div class="col-auto">
              <div class="flex-row align-center">{{ currencyId }}</div>
            </div>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>最低落札価格</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="2">
              <CFormInput
                class="text-end"
                maxLength="8"
                name="lowest_bid_accept_price"
                :append="currencyId"
                v-on:keypress="isNumber"
                v-on:focusin="
                  () => {
                    lowest_bid_accept_price = lowest_bid_accept_price
                      ? Base.localeString2Number(lowest_bid_accept_price)
                      : '';
                  }
                "
                v-on:focusout="
                  event => {
                    lowest_bid_accept_price = priceLocaleString(event);
                  }
                "
                v-model="lowest_bid_accept_price"
              />
            </CCol>
            <div class="col-auto">
              <div class="flex-row align-center">{{ currencyId }}</div>
            </div>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>数量</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="2">
              <CFormInput
                class="text-end"
                maxLength="8"
                name="quantity"
                v-on:keypress="isNumber"
                v-model="quantity"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>最低入札数量</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="2">
              <CFormInput
                class="text-end"
                maxLength="8"
                name="lowest_bid_quantity"
                v-on:keypress="isNumber"
                v-model="lowest_bid_quantity"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>最低落札数量</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="2">
              <CFormInput
                class="text-end"
                maxLength="8"
                name="lowest_bid_accept_quantity"
                v-on:keypress="isNumber"
                v-model="lowest_bid_accept_quantity"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>即決価格</label>
            </CCol>
            <CCol sm="2">
              <CFormInput
                class="text-end"
                maxLength="8"
                name="lowest_bid_accept_price"
                :append="currencyId"
                v-on:keypress="isNumber"
                v-on:focusin="
                  () => {
                    lowest_bid_accept_price = lowest_bid_accept_price
                      ? Base.localeString2Number(lowest_bid_accept_price)
                      : '';
                  }
                "
                v-on:focusout="
                  event => {
                    lowest_bid_accept_price = priceLocaleString(event);
                  }
                "
                v-model="lowest_bid_accept_price"
              />
            </CCol>
            <div class="col-auto">
              <div class="flex-row align-center">{{ currencyId }}</div>
            </div>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>SKU ID</label>
            </CCol>
            <CCol sm="2">
              <CFormInput name="sku_id" v-model="stockData.free_field.sku_id" />
            </CCol>
          </CRow>
        </CForm>

        <CForm onsubmit="return false;" v-show="!loading" class="ml-4 mr-0">
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>備考欄１</label>
            </CCol>
            <CCol sm="10">
              <CFormTextarea
                rows="10"
                maxLength="100"
                v-model="stockData.free_field.note1"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>備考欄２</label>
            </CCol>
            <CCol sm="10">
              <CFormTextarea
                rows="10"
                maxLength="100"
                v-model="stockData.free_field.note2"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>備考欄１（英語）</label>
            </CCol>
            <CCol sm="10">
              <CFormTextarea
                rows="10"
                maxLength="100"
                v-model="stockData.free_field.note1_en"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="fw-bolder d-flex align-items-start">
              <label>備考欄２（英語）</label>
            </CCol>
            <CCol sm="10">
              <CFormTextarea
                rows="10"
                maxLength="100"
                v-model="stockData.free_field.note2_en"
              />
            </CCol>
          </CRow>
        </CForm>
      </CCardBody>
      <CCardFooter>
        <CButton color="secondary" @click="goBack">一覧に戻る</CButton>
        <CButton
          :disabled="!btnUpdateEnabled || isReadOnly"
          class="mx-1"
          color="primary"
          @click="openRegistModal"
          >更新する</CButton
        >
      </CCardFooter>
    </CCard>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="registModal"
      @close="closeRegistModal"
    >
      <CModalHeader>
        <CModalTitle>{{ registModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!updateDataLoading && validateResult?.length === 0">
          この内容で{{ itemNo ? '更新' : '登録' }}してもよろしいですか？
        </div>
        <div v-if="updateDataLoading" class="my-3 text-center">
          <CSpinner />
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="registModal = false"
          color="dark"
          :disabled="updateDataLoading"
          v-if="validateResult?.length === 0"
          >キャンセル</CButton
        >
        <CButton
          @click="btnUpdate"
          color="primary"
          :disabled="updateDataLoading"
          v-if="validateResult?.length === 0"
          >OK
        </CButton>
        <CButton
          @click="closeRegistModal"
          color="primary"
          :disabled="updateDataLoading"
          v-if="validateResult?.length > 0"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>編集中止確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="cancelModal = false" color="dark">キャンセル</CButton>
        <CButton
          @click="
            () => {
              cancelModal = false;
              nextPage();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
  import Base from '@/common/base';
  import cloneDeep from 'lodash-es/cloneDeep';
  import {marked} from 'marked';
  import {onMounted, ref} from 'vue';
  import {onBeforeRouteLeave, useRoute, useRouter} from 'vue-router';
  import {useLotStore} from '../../../store/lot.js';
  import ImageUpload from './ImageUpload.vue';
  import {useAuthStore} from '@/store/auth';

  const route = useRoute();
  const router = useRouter();
  const {setSelectedExhibition} = useLotStore();
  const {isReadOnly} = useAuthStore();

  const itemNo = route.params.itemNo;

  const images = ref([]);
  const stockData = ref({
    picturePath: [],
    free_field: {
      category: null,
      maker: null,
      model: null,
      product_name: null,
      capacity: null,
      rank: null,
      sim: null,
      sku_id: null,
      note1: null,
      note2: null,
      note1_en: null,
      note2_en: null,
    },
  });
  const currencyId = ref(null);
  const quantity = ref(null);
  const lowest_bid_price = ref('');
  const lowest_bid_accept_price = ref('');
  const lowest_bid_quantity = ref(1);
  const lowest_bid_accept_quantity = ref(1);
  const loading = ref(false);
  const updateDataLoading = ref(false);
  const constantList = ref('');
  const optionsCategory = ref([]);
  const confirmMode = ref(false);
  const registModal = ref(false);
  const registModalTitle = ref('確認');
  const validateResult = ref([]);
  const btnUpdateEnabled = ref(true);
  // Changing check
  const stockDataOrig = ref(null);
  const nextPage = ref(null);
  const cancelModal = ref(false);
  const btnClicked = ref(false);

  onBeforeRouteLeave((to, from, next) => {
    console.log('beforeRouteLeave');
    if (Base.objectsAreIdentical(stockData.value, stockDataOrig.value)) {
      next();
    } else {
      nextPage.value = next;
      cancelModal.value = true;
    }
  });

  const getConstants = () => {
    optionsCategory.value = [];
    return Methods.apiExecute('get-constants-by-keys', {
      key_strings: ['PRODUCT_CATEGORY'],
    }).then(response => {
      constantList.value = response.data || [];
      optionsCategory.value = constantList.value
        .filter(x => x.key_string === 'PRODUCT_CATEGORY')
        .map(x => ({
          value: x.value1,
          label: x.value2,
        }));
      return Promise.resolve();
    });
  };

  const getStockData = () => {
    // Request to server
    const request = {
      item_no: Number(itemNo),
    };

    loading.value = true;

    return Methods.apiExecute('get-item-detail', request).then(response => {
      if (response.status === 200 && response.data.length === 1) {
        const tmpData = response.data[0];

        // 初期化
        images.value = [];

        stockData.value = {
          ...stockData.value,
          exhibition_no: tmpData.exhibition_no,
          lot_no: tmpData.lot_no,
          manage_no: tmpData.manage_no,
        };
        currencyId.value = tmpData.currency_id;
        quantity.value = tmpData.quantity;
        lowest_bid_price.value = tmpData.lowest_bid_price;
        lowest_bid_accept_price.value = tmpData.lowest_bid_accept_price;
        lowest_bid_quantity.value = tmpData.lowest_bid_quantity;
        lowest_bid_accept_quantity.value = tmpData.lowest_bid_accept_quantity;

        // Free_field
        const jpFields =
          tmpData.localized_json_array.find(x => x.f1 === 'ja')?.f2 || {};
        const enFields =
          tmpData.localized_json_array.find(x => x.f1 === 'en')?.f2 || {};
        stockData.value.free_field = {
          ...jpFields,
          note1_en: enFields.note1,
          note2_en: enFields.note2,
        };
        stockData.value.picturePath = [];

        // Prepare pictures and videos
        let picturePath = [];
        if (tmpData.ancillary_json_array) {
          const filter = tmpData.ancillary_json_array.filter(
            x => x.f1 === 'common' || x.f1 === 'ja'
          );
          filter.sort(ancillaryCompare);
          if (filter.length < 0) {
            return Promise.resolve(tmpData);
          }
          for (const idx in filter) {
            if (filter[idx].f4.indexOf('.mp4') === -1) {
              // 画像
              picturePath.push({
                type: 'image',
                name: Base.getFileName(filter[idx].f4),
                path:
                  import.meta.env.VITE_API_ENDPOINT.replace('api/', '') +
                  filter[idx].f4,
                postar: '',
              });
            } else {
              // 動画
              console.log(filter[idx].f4);
              picturePath.push({
                type: 'video',
                name: Base.getFileName(filter[idx].f4),
                path:
                  import.meta.env.VITE_API_ENDPOINT.replace('api/', '') +
                  filter[idx].f4,
                postar:
                  import.meta.env.VITE_API_ENDPOINT.replace('api/', '') +
                  filter[idx].f5,
              });
            }
          }

          // Video to bottom
          if (picturePath && picturePath.length > 0) {
            picturePath = picturePath.sort((a, b) => {
              // If video then move to bottom
              if (a.type === 'video' && b.type !== 'video') {
                return 1;
              }
              if (b.type === 'video' && a.type !== 'video') {
                return -1;
              }
              // Else sort by name
              return a.name > b.name ? 1 : -1;
            });
          }
          console.log('Sorted images: ', picturePath);
        }
        stockData.value.picturePath = picturePath;

        images.value = picturePath.map(x => {
          return Object.assign({}, x, {
            path: x.path.replace(
              process.env.VITE_API_ENDPOINT.replace('api/', ''),
              ''
            ),
            postar: x.postar.replace(
              process.env.VITE_API_ENDPOINT.replace('api/', ''),
              ''
            ),
          });
        });

        return Promise.resolve();
      }
      return Promise.reject();
    });
  };

  const goBack = () => {
    if (btnClicked.value) {
      return;
    }
    btnClicked.value = true;
    setSelectedExhibition(stockData.value.exhibition_no);
    router.push({path: '/exhibitions'}).catch(e => console.log(e));
  };

  const ancillaryCompare = (x, y) => {
    if (x.f3 < y.f3) {
      return -1;
    }
    if (x.f3 > y.f3) {
      return 1;
    }
    return 0;
  };

  const openRegistModal = () => {
    registModalTitle.value = '確認';
    registModal.value = true;
    updateDataLoading.value = false;
    validateResult.value = [];
  };

  const closeRegistModal = () => {
    registModal.value = false;
    updateDataLoading.value = false;
  };

  const btnUpdate = () => {
    console.log('btnUpdate');

    updateDataLoading.value = true;
    validateResult.value = [];

    /*
     * Sort images
     * Video to bottom
     */
    let tmpImages = images.value;
    if (images.value && images.value.length > 0) {
      tmpImages = tmpImages.sort((a, b) => {
        // If video then move to bottom
        if (a.type === 'video' && b.type !== 'video') {
          return 1;
        }
        if (b.type === 'video' && a.type !== 'video') {
          return -1;
        }
        // Else sort by name
        return a.name > b.name ? 1 : -1;
      });
    }

    const reqParam = {
      validate: false,
      data: {
        exhibition_no: stockData.value.exhibition_no,
        lot_no: stockData.value.lot_no,
        manage_no: stockData.value.manage_no,
        item_no: itemNo,
        quantity: Base.localeString2Number(quantity.value || 0),
        lowest_bid_price: Base.localeString2Number(lowest_bid_price.value || 0),
        lowest_bid_accept_price: Base.localeString2Number(
          lowest_bid_accept_price.value || 0
        ),
        lowest_bid_quantity: Base.localeString2Number(
          lowest_bid_quantity.value || 0
        ),
        lowest_bid_accept_quantity: Base.localeString2Number(
          lowest_bid_accept_quantity.value || 0
        ),
        free_field: stockData.value.free_field,
        picturePath: tmpImages,
      },
    };

    console.log('reqParam: ', reqParam);

    // Validate and update data
    Methods.apiExecute('upsert-item', reqParam)
      .then(response => {
        console.log('response.data: ', response.data);
        registModal.value = false;
        updateDataLoading.value = false;
        stockDataOrig.value = cloneDeep(stockData.value);

        // Go to list
        setSelectedExhibition(stockData.value.exhibition_no);
        router.push({path: '/exhibitions'});
      })
      .catch(error => {
        console.log(error);
        updateDataLoading.value = false;
        validateResult.value = Methods.parseHtmlResponseError(router, error);
      });
  };

  const onImageChange = files => {
    images.value = files.map(x => {
      if (x.path) {
        x.path = x.path.replace(
          import.meta.env.VITE_API_ENDPOINT.replace('api/', ''),
          ''
        );
      }
      return x;
    });
  };

  const onUploadStart = () => {
    console.log('onUploadStart');
    btnUpdateEnabled.value = false;
  };

  const onUploadFinish = () => {
    console.log('onUploadFinish');
    btnUpdateEnabled.value = true;
  };

  const isNumber = evt => {
    const e = evt ? evt : window.event;
    const charCode = e.which ? e.which : e.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
      return e.preventDefault();
    }
    return true;
  };

  const priceLocaleString = event => {
    return Base.priceLocaleString(event.target.value);
  };

  // マークダウンをHTMLに変換
  const parsedHTML = content => {
    const main_content = content || null;
    return main_content ? marked.parse(main_content) : null;
  };

  onMounted(async () => {
    loading.value = true;
    await getConstants();
    loading.value = false;
    if (itemNo) {
      console.log('商品情報');
      await getStockData();
      // 変更確認要
      stockDataOrig.value = cloneDeep(stockData.value);
      loading.value = false;
    } else {
      console.log('商品新規追加');
      stockData.value.exhibition_no = Number(route.params.exhibitionNo);
      stockData.value.free_field.category =
        optionsCategory.value && optionsCategory.value.length > 0
          ? optionsCategory.value[0].value
          : null;
      currencyId.value = '円';
      // 変更確認要
      stockDataOrig.value = cloneDeep(stockData.value);
    }
  });
</script>

<style lang="scss" scoped>
  label {
    line-height: 20px;
  }

  .swiper {
    .swiper-slide {
      background-size: cover;
      background-position: center;
    }

    &.gallery-top {
      height: 80%;
      width: 100%;
    }

    &.gallery-thumbs {
      height: 20%;
      box-sizing: border-box;
      padding: 2px 0;
    }

    &.gallery-thumbs .swiper-slide {
      width: 20%;
      height: 100%;
      opacity: 0.4;
    }

    &.gallery-thumbs .swiper-slide-active {
      opacity: 1;
    }
  }

  .textArea {
    margin: 0;
    font-family: inherit;
    background-color: #d8dbe0;
    opacity: 1;
    overflow: auto;
    resize: vertical;
    height: 300px;
  }
</style>
<style scoped lang="scss">
  .custom-form-radio {
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    background-clip: padding-box;
    color: #768192;
    background-color: #fff;
    border-color: #d8dbe0;
    border-radius: 0.25rem;
    -webkit-transition:
      border-color 0.15s ease-in-out,
      -webkit-box-shadow 0.15s ease-in-out;
    transition:
      border-color 0.15s ease-in-out,
      -webkit-box-shadow 0.15s ease-in-out;
    transition:
      border-color 0.15s ease-in-out,
      box-shadow 0.15s ease-in-out;
    transition:
      border-color 0.15s ease-in-out,
      box-shadow 0.15s ease-in-out,
      -webkit-box-shadow 0.15s ease-in-out;
  }
</style>
