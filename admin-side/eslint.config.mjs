import pluginJs from '@eslint/js'
import jsdoc from 'eslint-plugin-jsdoc'
import pluginVue from 'eslint-plugin-vue'
import commonRules from '../.eslintRules.js'

export default [
  {
    files: ['**/*.{js,mjs,cjs,vue}'],
  },
  {
    ignores: [
      'node_modules/*',
      '/dist/**'
    ],
  },
  pluginJs.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  jsdoc.configs['flat/recommended'],
  commonRules,
  {
    rules: {
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'vue/max-attributes-per-line': [
        'error', {
          'singleline': {
            'max': 1
          },
          'multiline': {
            'max': 1
          }
        }
      ],
    },
  },
]
