/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *TOP
 * *********************************************************************** */
#home > #main {
  position: relative;
  background-image: url("../img/home/<USER>");
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: top;
}
@media screen and (max-width: 767px) {
  #home > #main {
    background-image: url("../img/home/<USER>");
  }
}
#home > #main #news-headline {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 1.2rem 1rem 1.2rem;
  background-color: #e60012;
}
@media screen and (max-width: 767px) {
  #home > #main #news-headline {
    padding: 1rem 0 1rem;
  }
}
#home > #main #news-headline .wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1.5rem;
}
@media screen and (max-width: 767px) {
  #home > #main #news-headline .wrap {
    flex-direction: column;
    padding: 0 4vw;
  }
}
#home > #main #news-headline .wrap .label {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  color: #fff;
  background-color: #e60012;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #home > #main #news-headline .wrap .label {
    display: none;
  }
}
#home > #main #news-headline .wrap .label span {
  display: inline-block;
  padding: 1px 1rem 0;
  font-size: 11px;
  font-weight: bold;
  line-height: 1.6;
  background-color: #e60012;
  border: 1px solid #fff;
  border-radius: 4px;
}
#home > #main #news-headline .wrap dl {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  flex-grow: 1;
  margin: 0 auto;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #home > #main #news-headline .wrap dl {
    flex-direction: column;
    padding: 0.2rem 0 0;
  }
}
#home > #main #news-headline .wrap dl dt, #home > #main #news-headline .wrap dl dd {
  padding: 0;
  color: #fff;
  font-size: 14.4px;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #home > #main #news-headline .wrap dl dt, #home > #main #news-headline .wrap dl dd {
    line-height: 1.4;
    font-size: 3.5vw;
    font-weight: normal;
  }
}
#home > #main #news-headline .wrap dl dt {
  width: auto;
  padding: 0 1rem 0 1.5rem;
  font-weight: bold;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #home > #main #news-headline .wrap dl dt {
    width: 100%;
    margin: 0 0 0.3rem;
    padding: 0;
    font-weight: normal;
  }
}
#home > #main #news-headline .wrap dl dd {
  width: auto;
}
@media screen and (max-width: 767px) {
  #home > #main #news-headline .wrap dl dd {
    width: 100%;
    margin: 0 0 0.3rem;
  }
}
#home > #main #news-headline .wrap dl dd a {
  color: #fff;
  font-weight: bold;
}
@media screen and (max-width: 767px) {
  #home > #main #news-headline .wrap dl dd a {
    font-size: 3.5vw;
    font-weight: normal;
  }
}
#home > #main #heroes {
  width: 100%;
  height: 280px;
  background: url("../img/home/<USER>") center top/cover no-repeat;
}
@media screen and (max-width: 767px) {
  #home > #main #heroes {
    width: 100%;
    height: 30vw;
    background: url("../img/home/<USER>") center no-repeat;
    background-size: 100%;
  }
}
#home > #main #search-condition {
  position: relative;
}
@media screen and (max-width: 767px) {
  #home > #main #search-condition {
    padding: 15vw 4vw 7vw;
  }
}
#home > #main #search-condition .ttl-home {
  position: absolute;
  top: 44px;
  left: 50%;
  transform: translate(-50%, -0.7rem);
  margin: 0;
  font-size: 14.4px;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #home > #main #search-condition .ttl-home {
    top: 8.5vw;
  }
}
#home > #main #search-condition .ttl-home span {
  color: #fff;
  font-size: 14.4px;
  font-weight: bold;
}
@media screen and (max-width: 767px) {
  #home > #main #search-condition .ttl-home span {
    font-size: 3vw;
  }
}
#home > #main #nav-category {
  width: 1280px;
  max-width: calc(100% - 2rem);
  margin: 0 auto;
  padding: 1.5rem 0 3.5rem;
}
@media screen and (max-width: 767px) {
  #home > #main #nav-category {
    padding: 1rem 0 3rem;
  }
}
#home > #main #nav-category ul {
  display: flex;
  flex-direction: row;
  justify-content: left;
  flex-wrap: wrap;
}
#home > #main #nav-category ul li {
  position: relative;
  width: calc((100% - 4rem) / 5);
  height: 50px;
  margin: 0 1rem 0.7rem 0;
  padding: 0;
  background-color: #e60012;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #home > #main #nav-category ul li {
    width: calc((100% - 0.5rem) / 2);
    height: auto;
    margin: 0 0.5rem 0.5rem 0;
    font-size: 16px;
  }
}
#home > #main #nav-category ul li:nth-of-type(5n) {
  margin: 0 0 0.7rem 0;
}
@media screen and (max-width: 767px) {
  #home > #main #nav-category ul li:nth-of-type(5n) {
    margin: 0 0.5rem 0.5rem 0;
  }
}
@media screen and (max-width: 767px) {
  #home > #main #nav-category ul li:nth-of-type(2n) {
    margin: 0 0 0.5rem;
  }
}
#home > #main #nav-category ul li::after {
  position: absolute;
  top: calc(50% - 8px);
  right: 5px;
  color: #fff;
  font-size: 1rem;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 1;
  transform: rotate(0deg);
}
@media screen and (max-width: 767px) {
  #home > #main #nav-category ul li::after {
    top: calc(50% - 6px);
    right: 1px;
    font-size: 0.9rem;
  }
}
#home > #main #nav-category ul li a {
  display: flex;
  align-items: center;
  justify-content: left;
  width: 100%;
  height: 100%;
  padding: 0.5rem 1.5rem;
  color: #fff;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 2px;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #home > #main #nav-category ul li a {
    padding: 0.8rem 0.8rem;
    font-size: 0.9rem;
    letter-spacing: 1px;
  }
}
#home > #main #list-new {
  padding: 2rem 0 3.5rem;
  background-color: #f5f5f5;
}
@media screen and (max-width: 767px) {
  #home > #main #list-new {
    padding: 2rem 0.5rem 3.5rem 0.5rem;
  }
}
#home > #main #list-sale {
  width: calc(1280px + 2rem);
  max-width: 100%;
  margin: 0 auto;
  padding: 2rem 0 3.5rem;
  position: relative;
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale {
    padding: 1.5rem 1rem 1rem;
  }
}
#home > #main #list-sale .container {
  width: auto;
}
#home > #main #list-sale .container .item-list {
  margin: 0;
  text-align: center;
}
#home > #main #list-sale .container .item-list.no-item {
  padding: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}
#home > #main #list-sale .container .item-list.no-item p.no-item-msg {
  text-align: center;
  font-size: 22px;
  font-weight: 700;
  color: #000;
}
#home > #main #list-sale .container .item-list ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 0 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul {
    padding: 0;
  }
}
#home > #main #list-sale .container .item-list ul li {
  width: calc((100% - 60px) / 4);
  margin: 0 15px 15px 0;
  background-color: #fff;
  border: 1px solid #e5e5e5;
}
@media screen and (max-width: 1080px) {
  #home > #main #list-sale .container .item-list ul li {
    width: calc((100% - 45px) / 3);
  }
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul li {
    width: calc((100% - 15px) / 2);
  }
}
#home > #main #list-sale .container .item-list ul li:nth-child(4n) {
  margin: 0 15px 15px 0;
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul li:nth-child(4n) {
    margin: 0 15px 15px 0;
  }
}
#home > #main #list-sale .container .item-list ul li:nth-child(2n) {
  margin: 0 15px 15px 0;
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul li:nth-child(2n) {
    margin: 0 0 15px 0;
  }
}
#home > #main #list-sale .container .item-list ul li.soldout a {
  position: relative;
}
#home > #main #list-sale .container .item-list ul li.soldout a figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/icn_soldout.png);
  background-size: 92% auto;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;
}
#home > #main #list-sale .container .item-list ul li a {
  display: block;
  height: 100%;
  position: relative;
}
#home > #main #list-sale .container .item-list ul li a figure {
  position: relative;
  aspect-ratio: 1;
}
#home > #main #list-sale .container .item-list ul li a figure img {
  height: 100%;
  object-fit: cover;
}
#home > #main #list-sale .container .item-list ul li a figure .favorite {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 34px;
  height: 34px;
  padding: 2px 2px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
  background-image: url(../img/common/icn_favorite.svg);
  background-size: 26px auto;
  background-repeat: no-repeat;
  background-position: 3px 2px;
}
#home > #main #list-sale .container .item-list ul li a figure .favorite::hover {
  opacity: 0.7;
}
#home > #main #list-sale .container .item-list ul li a figure .favorite.active {
  background-image: url(../img/common/icn_favorite_on.svg);
  background-size: 26px auto;
  background-repeat: no-repeat;
  background-position: 3px 2px;
}
#home > #main #list-sale .container .item-list ul li a .item-name {
  margin: 0 auto;
  padding: 0.5rem;
  color: #fff;
  background-color: #444;
  font-size: 0.8rem;
  font-weight: 700;
  line-height: 1.4;
  border: none;
}
#home > #main #list-sale .container .item-list ul li a .current-price {
  padding: 0.5rem;
}
#home > #main #list-sale .container .item-list ul li a .current-price .price-c {
  color: #231914;
  font-size: 12px;
}
#home > #main #list-sale .container .item-list ul li a .current-price .price-v {
  color: #E50A09;
  font-size: 1rem;
  font-weight: 700;
}
#home > #main #list-sale .container .item-list ul li a .current-price .price-u {
  color: #E50A09;
  font-size: 1rem;
  font-weight: 700;
}
#home > #main #list-sale .container .item-list ul li a figure img {
  width: 100%;
}
#home > #main #list-sale .container .item-list ul li a dl {
  display: flex;
  flex-direction: row;
  width: 100%;
  padding: 0;
}
#home > #main #list-sale .container .item-list ul li a dl dt, #home > #main #list-sale .container .item-list ul li a dl dd {
  background-color: #E8E7E7;
}
#home > #main #list-sale .container .item-list ul li a dl dt {
  width: 90px;
  margin: 0 1px 0 0;
  padding: 0.5rem 0.5rem 0.5rem 2.5rem;
  font-size: 0.8rem;
  background: url("../img/common/icn_hammer_list.png") no-repeat;
  background-size: 22px 24px;
  background-color: #e7e7e7;
  background-position: 10px 50%;
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul li a dl dt {
    width: 60px;
    padding: 3px;
    font-size: 0.7rem;
    background-size: 18px 20px;
    background-position: 7px 10px;
  }
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul li a dl dt .bid-l {
    padding: 8px 0 0 26px;
  }
}
#home > #main #list-sale .container .item-list ul li a dl dt .bid-v {
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul li a dl dt .bid-v {
    width: 100%;
    padding: 5px 0 0;
    font-size: 0.9rem;
    text-align: center;
  }
}
#home > #main #list-sale .container .item-list ul li a dl dd {
  width: calc(100% - 91px);
  margin: 0;
  padding: 0.5rem 0.5rem 0.5rem 2.7rem;
  font-size: 0.8rem;
  background: url("../img/common/icn_clock_list.png") no-repeat;
  background-size: 24px 24px;
  background-color: #e7e7e7;
  background-position: 10px 50%;
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul li a dl dd {
    width: calc(100% - 61px);
    padding: 3px;
    font-size: 0.7rem;
    background-image: none;
  }
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul li a dl dd .end-l {
    padding: 3px;
  }
}
#home > #main #list-sale .container .item-list ul li a dl dd .end-l span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
#home > #main #list-sale .container .item-list ul li a dl dd .end-l span.value {
  display: inline-block;
  font-size: 0.8rem;
  font-weight: 700;
}
#home > #main #list-sale .container .item-list ul li a dl dd .end-l span.limit {
  color: #ff0000;
}
@media screen and (max-width: 767px) {
  #home > #main #list-sale .container .item-list ul li a dl dd .end-v {
    width: 100%;
    padding: 3px;
  }
}
#home > #main #list-sale .container .item-list ul li a dl dd .end-v span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
#home > #main #list-sale .container .item-list ul li a dl dd .end-v span.value {
  display: inline-block;
  font-size: 0.8rem;
  font-weight: 700;
}
#home > #main #list-sale .container .item-list ul li.is-hidden {
  opacity: 0;
  height: 0;
  margin: 0;
}
#home > #main #info {
  padding: 80px 1rem;
  background-color: #f5f5f5;
}
@media screen and (max-width: 767px) {
  #home > #main #info {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}
#home > #main #info .container {
  width: 1160px;
}
#home > #main #info [class^=info-box-] + [class^=info-box-] {
  margin-top: 30px;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] + [class^=info-box-] {
    margin-top: 25px;
  }
}
#home > #main #info [class^=info-box-] h2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 160px 12px 10px;
  border-bottom: 2px solid #ccc;
  position: relative;
  font-size: 1.8rem;
  font-weight: bold;
  min-height: 70px;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] h2 {
    min-height: 50px;
    padding: 0.5rem calc(100px + 1rem) 0.8rem 0.5rem;
    font-size: 4.8vw;
  }
}
#home > #main #info [class^=info-box-] .info-item {
  padding: 0 0 2rem;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] .info-item {
    padding: 0.2rem 0.5rem 1rem;
  }
}
#home > #main #info [class^=info-box-] .info-item li.is-hidden {
  display: none;
  opacity: 0;
  height: 0;
  margin: 0;
}
#home > #main #info [class^=info-box-] .info-item li a.headline {
  display: flex;
  flex-direction: row;
  padding: 1.5rem calc(1rem + 80px) 1.5rem 1rem;
  margin: 0;
  position: relative;
  letter-spacing: 0;
  border-bottom: 1px solid #ececec;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] .info-item li a.headline {
    flex-direction: column;
    margin: 0;
    padding: 15px 0 20px;
    font-size: 1rem;
    justify-content: flex-start;
  }
}
#home > #main #info [class^=info-box-] .info-item li a.headline:hover {
  color: #e60012;
}
#home > #main #info [class^=info-box-] .info-item li a.headline .status {
  width: 80px;
  color: #e60012;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] .info-item li a.headline .status {
    display: inline;
    width: 100%;
    text-align: left;
  }
}
#home > #main #info [class^=info-box-] .info-item li a.headline .status span {
  font-weight: bold;
}
#home > #main #info [class^=info-box-] .info-item li a.headline .notice-day {
  width: 130px;
  padding: 0 1rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] .info-item li a.headline .notice-day {
    width: 100%;
    margin: 0 0 0.3rem;
    padding: 0;
    color: #909090;
    font-size: 3.5vw;
    text-align: left;
  }
}
#home > #main #info [class^=info-box-] .info-item li a.headline .ttl {
  width: calc(100% - 80px - 130px);
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] .info-item li a.headline .ttl {
    width: 100%;
    font-size: 3.5vw;
    line-height: 1.6;
  }
}
#home > #main #info [class^=info-box-] .newslist-more {
  text-align: center;
}
#home > #main #info [class^=info-box-] .newslist-more button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  width: 280px;
  margin: 0 auto;
  padding: 3px 15px 3px 12px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #fff;
  line-height: 1;
  border-radius: 4px;
  background-color: #444;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] .newslist-more button {
    top: calc(50% - 17px);
    width: 280px;
    height: 60px;
    padding: 5px 8px;
    font-size: 3.8vw;
  }
}
#home > #main #info [class^=info-box-] .newslist-more button .txt {
  position: relative;
  display: inline-block;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] .newslist-more button .txt {
    font-size: 3.8vw;
  }
}
#home > #main #info [class^=info-box-] .newslist-more button .txt .arrow {
  position: absolute;
  top: calc(50% - 10px);
  right: -20px;
  width: 18px;
  height: 18px;
}
#home > #main #info [class^=info-box-] .newslist-more button .txt .arrow:after {
  position: absolute;
  top: calc(50% - 7px);
  right: 0;
  color: #fff;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  transform: rotateZ(90deg);
  line-height: 1;
}
#home > #main #info [class^=info-box-] em[class^=cate-] {
  display: inline-block;
  padding: 1px;
  color: #fff;
  font-weight: 400;
  font-size: 1.1rem;
  border-radius: 4px;
  margin-right: 30px;
  width: 66px;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] em[class^=cate-] {
    font-size: 11px;
    margin-right: 10px;
    margin-right: 0;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60px;
  }
}
#home > #main #info [class^=info-box-] p.bid-day {
  display: inline-block;
  margin-left: 20px;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] p.bid-day {
    font-size: 14px;
    display: block;
    margin-left: 0;
  }
}
#home > #main #info [class^=info-box-] p.notice-day {
  margin: 0;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #home > #main #info [class^=info-box-] p.notice-day {
    display: block;
    font-size: 0.8rem;
  }
}
/*# sourceMappingURL=home.css.map */
