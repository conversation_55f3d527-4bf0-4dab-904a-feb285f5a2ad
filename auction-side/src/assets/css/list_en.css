@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *一覧ページ SP 英語表記対応
 * *********************************************************************** */
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.favorite-selected:before {
    content: "Remove Favorite：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.maker:before {
    content: "Maker：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.date-bid:before {
    content: "Date of Bid：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.itemname:before {
    content: "Item：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.sim:before {
    content: "SIM：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.capacity:before {
    content: "Memory：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.color:before {
    content: "Color：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.grade:before {
    content: "Grade：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.quantity:before {
    content: "amount：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.quantity-min:before {
    content: "Minimum Amount：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.price-min:before {
    content: "Minimum Price per Unit：";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check:before {
    content: "Add to Favorites：";
  }
}
/*# sourceMappingURL=list_en.css.map */