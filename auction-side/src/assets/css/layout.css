@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *wrap
 * ***********************************************************************/
.container {
  width: 1280px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
}
@media screen and (max-width: 767px) {
  .container {
    width: 100%;
    max-width: 100%;
    padding: 0;
  }
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *header
 * ***********************************************************************/
header {
  border-bottom: 4px solid #e60012;
  background-color: #fff;
  position: relative;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
}
@media screen and (max-width: 767px) {
  header {
    height: 60px;
    border-bottom: none;
    -webkit-box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.15);
            box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.15);
  }
}
header .wrap-header-elm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 1280px;
  height: auto;
  min-height: 60px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm {
    width: 100%;
    max-width: 100%;
    height: 60px;
    margin: 0;
    padding: 0;
  }
}
header .wrap-header-elm .h-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  margin: 0;
  padding: 0;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .h-top {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    width: 100%;
    mim-width: 100%;
    min-height: 35px;
    height: auto;
    -webkit-box-flex: 1;
        -ms-flex: auto;
            flex: auto;
  }
}
header .wrap-header-elm .h-top .caption {
  font-size: 12px;
}
header .wrap-header-elm .h-top .h-top-logo {
  width: 220px;
  margin: 0 0 0.5rem;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .h-top .h-top-logo {
    max-height: 50px;
    margin: 0;
    padding: 0 15px;
  }
}
header .wrap-header-elm .h-top .h-top-logo a.logo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: auto;
  -webkit-transition: none;
  transition: none;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .h-top .h-top-logo a.logo {
    max-width: 150px;
  }
}
header .wrap-header-elm .h-top .h-top-logo a.logo img {
  width: 100%;
  height: auto;
}
header .wrap-header-elm .h-top .h-top-logo p {
  color: #fff;
  font-size: 16px;
  position: relative;
  line-height: 1;
  margin-left: 22px;
  padding-left: 20px;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .h-top .h-top-logo p {
    font-size: 10px;
    margin-left: 11px;
    padding-left: 10px;
  }
  header .wrap-header-elm .h-top .h-top-logo p::before {
    width: 1px;
    height: 15px;
  }
}
header .wrap-header-elm .h-top .h-top-logo p::before {
  content: "";
  display: block;
  width: 2px;
  height: 28px;
  background-color: #fff;
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
header .wrap-header-elm .nav-elm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-negative: 2;
      flex-shrink: 2;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 0 0 1rem;
  padding: 1rem 0;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .nav-elm {
    margin: 0 0 0 auto;
    padding: 0;
  }
}
header .wrap-header-elm .nav-elm .nav-category {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  width: auto;
  height: 42px;
  margin: 0 0 0.5rem;
  color: #444;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .nav-elm .nav-category {
    display: none;
  }
}
header .wrap-header-elm .nav-elm .nav-category > li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  width: 100%;
  height: 42px;
  padding: 0;
  color: #fff;
  font-size: 12px;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-right: none;
  border-top-left-radius: 30px;
  border-bottom-left-radius: 30px;
  list-style-type: none;
}
header .wrap-header-elm .nav-elm .nav-category > li a {
  padding: 0 2.4rem 0 1.8rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: 100%;
  height: 100%;
  color: #444;
  font-weight: bold;
}
header .wrap-header-elm .nav-elm .nav-category:after {
  content: "";
  position: absolute;
  top: calc(50% - 3px);
  right: 15px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 7px solid #cfcfcf;
}
header .wrap-header-elm .nav-elm .nav-category:hover {
  background-color: #main-gray;
}
header .wrap-header-elm .nav-elm .nav-category:hover li .category-list {
  visibility: visible;
  -webkit-transform: scaleY(1);
          transform: scaleY(1);
  opacity: 1;
}
header .wrap-header-elm .nav-elm .nav-category li .category-list {
  visibility: hidden;
  -webkit-transform: scaleY(0);
          transform: scaleY(0);
  -webkit-transform-origin: center top;
          transform-origin: center top;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  opacity: 0;
  position: absolute;
  top: 42px;
  z-index: 20;
}
header .wrap-header-elm .nav-elm .nav-category li .category-list ul {
  width: 180px;
  background-color: #fff;
  border-radius: 4px;
  -webkit-box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.15);
          box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.15);
}
header .wrap-header-elm .nav-elm .nav-category li .category-list ul li {
  width: 100%;
}
header .wrap-header-elm .nav-elm .nav-category li .category-list ul li a {
  position: relative;
  display: block;
  width: 100%;
  padding: 15px 30px 15px 12px;
  color: #444;
  font-weight: bold;
  border-bottom: 1px solid #eee;
  line-height: 1.2;
}
header .wrap-header-elm .nav-elm .nav-category li .category-list ul li a:hover {
  opacity: 1;
  color: #e60012;
  background-color: #f5f5f5;
}
header .wrap-header-elm .nav-elm .nav-category li .category-list ul li a:hover:after {
  color: #e60012;
}
header .wrap-header-elm .nav-elm .nav-category li .category-list ul li a:after {
  position: absolute;
  top: calc(50% - 5px);
  right: 12px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #ccc;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
header .wrap-header-elm .nav-elm .nav-category li .category-list ul li:first-child a {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
header .wrap-header-elm .nav-elm .nav-category li .category-list ul li:last-child a {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
header .wrap-header-elm .nav-elm .search-elm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 42px;
  margin: 0 0.5rem 0.5rem 0;
}
@media screen and (max-width: 1080px) {
  header .wrap-header-elm .nav-elm .search-elm {
    display: none;
  }
}
header .wrap-header-elm .nav-elm .search-elm input {
  width: 200px;
  max-width: 100%;
  height: 42px;
  padding: 0.5rem 0.8rem;
  font-size: 0.8rem;
  border: 1px solid #ccc;
  border-radius: 0;
  border-right: none;
  line-height: 4;
}
header .wrap-header-elm .nav-elm .search-elm button {
  position: relative;
  width: 48px;
  height: 42px;
  border: 1px solid #ccc;
  border-left: none;
  border-top-right-radius: 30px;
  border-bottom-right-radius: 30px;
  background-color: #fff;
}
header .wrap-header-elm .nav-elm .search-elm button img {
  width: 20px;
  height: auto;
}
header .wrap-header-elm .nav-elm .user-name {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  margin: 0 1rem 0 0;
}
header .wrap-header-elm .nav-elm .user-name p {
  margin: 0 0 0.3rem;
  font-size: 0.7rem;
  line-height: 1.1;
}
header .wrap-header-elm .nav-elm .user-name p.welcome {
  display: block;
  width: 100%;
  text-align: left;
}
header .wrap-header-elm .nav-elm .user-name p span {
  display: inline-block;
}
header .wrap-header-elm .nav-elm .user-name p span.nickname a {
  font-size: 0.8rem;
  font-weight: 500;
  color: #e60012;
}
header .wrap-header-elm .nav-elm .user-name p span.nickname a:hover {
  text-decoration: underline;
}
header .wrap-header-elm .nav-elm .lang-wrap {
  position: relative;
  margin: 0 0 0.5rem 1rem;
  width: 88px;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .nav-elm .lang-wrap {
    width: auto;
    min-height: 24px;
    margin: 0 0.5rem 0 0;
  }
}
header .wrap-header-elm .nav-elm .lang-wrap:after {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  border-top: 3px solid #ccc;
  border-left: 3px solid #ccc;
  -webkit-transform: rotate(-135deg);
          transform: rotate(-135deg);
  position: absolute;
  right: 4px;
  top: calc(50% - 5px);
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .nav-elm .lang-wrap:after {
    display: none;
  }
}
header .wrap-header-elm .nav-elm .lang-wrap .lang {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 0 0 0 42px;
  font-weight: bold;
  text-decoration: underline;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: url("../img/common/icn_global.png") no-repeat 5px 6px;
  background-size: 29px 29px;
  border: none;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  header .wrap-header-elm .nav-elm .lang-wrap .lang {
    width: auto;
    margin: 0;
    padding: 0 0 0 36px;
    background: url("../img/common/icn_global.png") no-repeat 5px 3px;
    background-size: 20px 20px;
  }
}
header .wrap-header-elm .nav-elm .lang-wrap .lang option {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  padding: 0;
}
header .wrap-header-elm .nav-elm .nav-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 0 0 0.5rem 1rem;
}
header .wrap-header-elm .nav-elm .nav-btn .nav-account {
  width: auto;
  height: 42px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0 0 0 0.5rem;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
header .wrap-header-elm .nav-elm .nav-btn .nav-account a {
  height: 42px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0 1.5rem;
  text-align: center;
  border-radius: 4px;
}
header .wrap-header-elm .nav-elm .nav-btn .nav-account.registration a {
  color: #fff;
  background-color: #e60012;
}
header .wrap-header-elm .nav-elm .nav-btn .nav-account.login a {
  color: #fff;
  background-color: #009DE5;
}
header .wrap-header-elm .nav-elm .nav-btn .nav-account.logout a {
  color: #444;
  background-color: #fafafa;
  border: 1px solid #ccc;
}
header .wrap-header-elm .nav-elm .nav-btn .nav-account span {
  width: 100%;
  display: block;
  font-size: 0.9rem;
  font-weight: bold;
  text-align: center;
  line-height: 1;
}
header .wrap-header-nav-elm {
  padding: 0;
}
header .wrap-header-nav-elm .gnavi__wrap {
  width: 1280px;
  max-width: 100%;
  margin: 0 auto;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0 1rem;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list {
  position: relative;
  width: auto;
  max-width: 100%;
  height: 60px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list:hover {
  color: #e60012;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list:hover .dropdown__lists {
  visibility: visible;
  opacity: 1;
  -webkit-transform: scaleY(1);
          transform: scaleY(1);
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list:not(:first-child)::before {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 1.5rem;
  color: #444;
  font-size: 16px;
  font-weight: bold;
  text-decoration: none;
  letter-spacing: 0.05em;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  line-height: 1.2;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list a.arrow {
  padding: 0 2.5rem 0 1.5rem;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list a:hover {
  color: #e60012;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list a:hover.arrow:before {
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-top: 3px solid #e60012;
  border-left: 3px solid #e60012;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list a.arrow:before {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  border-top: 3px solid #ccc;
  border-left: 3px solid #ccc;
  -webkit-transform: rotate(-135deg);
          transform: rotate(-135deg);
  position: absolute;
  right: 20px;
  top: calc(50% - 5px);
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list .dropdown__lists {
  -webkit-transform: scaleY(0);
          transform: scaleY(0);
  -webkit-transform-origin: center top;
          transform-origin: center top;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  visibility: hidden;
  opacity: 0;
  width: 220px;
  min-width: 160px;
  background-color: #fff;
  -webkit-box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.15);
          box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  position: absolute;
  top: 60px;
  left: 0;
  z-index: 2;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list .dropdown__lists .dropdown__list {
  background-color: #fff;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  position: relative;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list .dropdown__lists .dropdown__list:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  padding-top: 0.7rem;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list .dropdown__lists .dropdown__list:last-child {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  padding-bottom: 0.95rem;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list .dropdown__lists .dropdown__list a {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: left;
      -ms-flex-align: left;
          align-items: left;
  padding: 0.4rem 1.2rem;
  color: #444;
  font-size: 0.9rem;
  text-decoration: none;
  line-height: 1.2;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list .dropdown__lists .dropdown__list a:hover {
  color: #e60012;
}
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list .dropdown__lists .dropdown__list a:before {
  display: none;
}

/* ヘッダー
 * *========================================== */
/* ---------------------------
 * *SPハンバーガーメニュー
 * *----------------------------- */
header .h-top p.btnMenu {
  position: relative;
  width: 60px;
  height: 60px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 60px;
          flex: 0 0 60px;
}
header .h-top p.btnMenu span.ham::before {
  content: "";
  display: block;
  background-color: #444;
  width: 36px;
  height: 2px;
  border-radius: 2px;
  position: absolute;
  top: 29px;
  right: 50%;
  -webkit-transform: translateX(50%);
          transform: translateX(50%);
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
header .h-top p.btnMenu::before, header .h-top p.btnMenu::after {
  content: "";
  display: block;
  background-color: #444;
  width: 36px;
  height: 2px;
  border-radius: 2px;
  position: absolute;
  right: 50%;
  -webkit-transform: translateX(50%);
          transform: translateX(50%);
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
header .h-top p.btnMenu::before {
  top: 21px;
}
header .h-top p.btnMenu::after {
  bottom: 21px;
}
header .h-top p.btnMenu.close::before {
  top: 50%;
  -webkit-transform: translate(50%, -50%) rotate(45deg);
          transform: translate(50%, -50%) rotate(45deg);
}
header .h-top p.btnMenu.close::after {
  top: 50%;
  bottom: auto;
  -webkit-transform: translate(50%, -50%) rotate(-45deg);
          transform: translate(50%, -50%) rotate(-45deg);
}
header .h-top p.btnMenu.close span.ham::before {
  display: none;
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}

/* グロナビ
 * *========================================== */
/* ---------------------------
 * *PC
 * *-----------------------------
 *
 * header .gNav
 *  background-color: #fff
 *  border-bottom: 1px solid #d9d9d9
 *  display: block !important
 *
 *  nav
 *    a
 *      font-size: 14px
 *      font-weight: 700
 *
 *    > ul
 *      display: flex
 *
 *      > li
 *        border-left: 1px solid #d9d9d9
 *        width: calc(100% / 9)
 *        height: 50px
 *        position: relative
 *
 *        &:last-of-type
 *          border-right: 1px solid #d9d9d9
 *
 *        &::after
 *          content: ""
 *          display: block
 *          width: 100%
 *          height: 2px
 *          position: absolute
 *          bottom: -1px
 *          left: 0
 *          right: 0
 *          background-color: transparent
 *          transition: all 0.08s linear
 *
 *        &:hover::after
 *          background-color: #01a7ac
 *
 *        > a
 *          display: flex
 *          align-items: center
 *          justify-content: center
 *          width: 100%
 *          height: 100%
 *          padding: 5px 10px
 *
 *        &.nav-black > a
 *          background-color: #333F48
 *          color: #fff
 *
 *        > a:hover
 *          opacity: 1
 *
 *        width: calc((100% - 80px - 130px) / 8)
 *
 *        &.nav-top
 *          width: 80px
 *
 *        &.nav-first
 *          width: 130px
 *
 *        > ul
 *          display: none
 *          position: absolute
 *          top: 51px
 *          left: 0
 *          background-color: rgba(255, 255, 255, 0.9)
 *          width: 100%
 *          padding: 15px 0
 *          z-index: 1
 *
 *        &.nav-access > ul
 *          width: 190px
 *
 *          /* 親メニュ幅より大きく
 *
 *        &.nav-entry > ul
 *          width: 125px
 *
 *          /* 親メニュ幅より大きく
 *
 *        &.nav-overview > ul
 *          width: 140px
 *
 *          /* 親メニュ幅より大きく
 *
 *        &:hover > ul
 *          display: block
 *          animation-name: fade-basic
 *          animation-duration: .5s
 *
 *        > ul li a
 *          display: block
 *          padding: 7px 15px
 *          color: #000 */
/* ---------------------------
 * *SP
 * *----------------------------- */
@media screen and (max-width: 767px) {
  header .gNav {
    display: none;
    position: absolute;
    top: 60px;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    /*height: -webkit-fill-available*/
    background-color: #fff;
    z-index: 100;
  }
  header .gNav nav {
    background-color: #f5f5f5;
  }
  header .gNav nav a {
    font-size: 16px;
  }
  header .gNav nav > ul > li {
    border-bottom: 1px solid #d8d8d8;
  }
  header .gNav nav > ul > li > a {
    min-height: 52px;
    padding: 5px 40px 5px 30px;
    position: relative;
  }
  header .gNav nav > ul > li > p {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    min-height: 52px;
    padding: 2vw 12vw 2vw 4vw;
    font-size: 3.8vw;
    font-weight: 700;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  header .gNav nav > ul > li.search {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    padding: 0.8rem 1rem;
  }
  header .gNav nav > ul > li.search input {
    height: 44px;
    width: calc(100% - 45px);
    padding: 11px 18px 13px;
    border-right: none;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav > ul > li.search input {
    border: none;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav > ul > li.search button {
    width: 43px;
    height: 44px;
    background-color: #fff;
    border: 1px solid #e4e4e4;
    border-left: none;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav > ul > li.search button {
    border: none;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav > ul > li.nav-black > a, header .gNav nav > ul > li.nav-black > p {
    background-color: #f5f5f5;
    color: #444;
  }
  header .gNav nav > ul > li > a::after {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
  }
  header .gNav nav > ul > li > p::before, header .gNav nav > ul > li > p::after {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
  }
  header .gNav nav > ul > li:not(.nav-black) > ul li a::after {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
  }
  header .gNav nav > ul > li:not(.nav-black) > ul li a::after {
    width: 8px;
    height: 8px;
    border-top: #444 2px solid;
    border-right: #444 2px solid;
    -webkit-transform: rotate(45deg) translateY(-50%);
            transform: rotate(45deg) translateY(-50%);
    right: 28px;
  }
  header .gNav nav > ul > li > a::after {
    width: 8px;
    height: 8px;
    border-top: #aeaeae 2px solid;
    border-right: #aeaeae 2px solid;
    -webkit-transform: rotate(45deg) translateY(-50%);
            transform: rotate(45deg) translateY(-50%);
    right: 28px;
  }
  header .gNav nav > ul > li.nav-black > a::after {
    border-top-color: #fff;
    border-right-color: #fff;
  }
  header .gNav nav > ul > li.nav-black > p::before, header .gNav nav > ul > li.nav-black > p::after {
    background-color: #aeaeae;
  }
  header .gNav nav > ul > li.nav-black ul {
    display: none;
    padding: 0;
    border-top: 1px solid #d8d8d8;
    background-color: #fff;
  }
  header .gNav nav > ul > li.nav-black ul li {
    border-top: none;
    display: block;
    width: 100%;
  }
  header .gNav nav > ul > li.nav-black ul li + li {
    border-top: 1px solid #d8d8d8;
  }
  header .gNav nav > ul > li.nav-black ul li:first-child > a {
    border-top: none;
  }
  header .gNav nav > ul > li.nav-black ul li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%;
    color: #5d635e;
    font-size: 3.5vw;
    min-height: 52px;
    padding: 2vw 12vw 2vw 4vw;
    position: relative;
  }
  header .gNav nav > ul > li.nav-black ul li a:after {
    position: absolute;
    top: calc(50% - 4px);
    right: 24px;
    display: block;
    width: 10px;
    height: 10px;
    padding: 0;
    color: #aeaeae;
    font-size: 16px;
    font-weight: 900;
    font-family: "Material Icons";
    content: "\e5cc";
    line-height: 0.6;
  }
  header .gNav nav > ul > li.user-name {
    border-bottom: none;
  }
  header .gNav nav > ul > li.user-name p {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: flex-end;
    -webkit-box-pack: flex-first;
        -ms-flex-pack: flex-first;
            justify-content: flex-first;
    min-height: 30px;
    padding: 15px 2.5rem 5px 1rem;
  }
  header .gNav nav > ul > li.user-name p:before, header .gNav nav > ul > li.user-name p:after {
    display: none;
  }
  header .gNav nav > ul > li.user-name p span {
    display: inline-block;
    margin: 0 0.5rem 0 0;
    color: #444;
    font-size: 0.7rem;
    line-height: 1.1;
  }
  header .gNav nav > ul > li.user-name p span.nickname a {
    color: #1989bc;
    font-size: 0.9rem;
    font-weight: bold;
    text-decoration: underline;
  }
  header .gNav nav > ul > li > p::before {
    width: 10px;
    height: 2px;
    background-color: #000;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
    right: 20px;
  }
  header .gNav nav > ul > li > p::after {
    width: 2px;
    height: 10px;
    background-color: #000;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
    right: 24px;
  }
  header .gNav nav > ul > li > p.close::after {
    display: none;
  }
  header .gNav nav > ul > li > p.ttl {
    padding: 0;
  }
  header .gNav nav > ul > li > p.ttl:before, header .gNav nav > ul > li > p.ttl:after {
    display: none;
  }
  header .gNav nav > ul > li > p.ttl a {
    display: block;
    width: 100%;
    height: 100%;
    padding: 5px 1rem;
    color: #444;
    font-weight: 700;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  header .gNav nav > ul > li > p.ttl a {
    font-size: 3.8vw;
  }
}
@media screen and (max-width: 767px) {
  header .gNav nav > ul > li:not(.nav-black) > ul li a {
    padding-left: calc(30px + 1em);
  }
  header .gNav nav .list-sns > ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: 100%;
    margin: 0 auto;
    padding: 3rem 1rem 5rem;
    background-color: #fff;
  }
  header .gNav nav .list-sns > ul li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: 46px;
    height: 46px;
    margin: 0 0.2rem 0.5rem;
  }
  header .gNav nav .list-sns > ul li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: 100%;
    height: 100%;
  }
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *main
 * *********************************************************************** */
#main {
  display: block;
}
#main.stock, #main.auction {
  padding-bottom: 60px;
}
@media screen and (max-width: 767px) {
  #main.stock, #main.auction {
    padding-bottom: 40px;
  }
}
#main #pNav {
  padding: 0;
  background-color: #fff;
  border-bottom: 1px solid #ccc;
}
#main #pNav > ul {
  width: 100%;
  max-width: 1280px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding: 14px 1rem;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #main #pNav > ul {
    width: auto;
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    overflow: scroll;
  }
}
#main #pNav > ul li {
  position: relative;
  font-size: 13px;
}
@media screen and (max-width: 767px) {
  #main #pNav > ul li {
    font-size: 3vw;
    white-space: nowrap;
  }
}
#main #pNav > ul li:not(:last-of-type) {
  margin: 0 1.7rem 0 0;
}
#main #pNav > ul li:not(:last-of-type)::after {
  position: absolute;
  top: calc(50% - 6px);
  right: -1.3rem;
  font-family: "Material Icons";
  content: "\e5cc";
  display: inline-block;
  font-weight: 900;
  line-height: 1;
}
#main #pNav > ul li a {
  color: #e60012;
}
#main #pNav > ul li a:hover {
  text-decoration: underline;
}

/* パンくずリスト
 * *==========================================
 *
 * #main #pNav > ul
 *  @include sp
 *    overflow-x: auto
 *    word-break: keep-all
 *    white-space: wrap
 *    -webkit-overflow-scrolling: touch */
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *footer
 * *********************************************************************** */
footer {
  position: relative;
  background-color: #fff;
  color: #444;
  padding: 60px 0 0;
  border-top: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  footer {
    padding: 0;
  }
}
footer #page_top {
  position: fixed;
  display: block;
  width: 60px;
  height: 60px;
  right: 1.5rem;
  bottom: 1rem;
  background-color: #e60012;
  border-radius: 50%;
  z-index: 10;
}
@media screen and (max-width: 767px) {
  footer #page_top {
    width: 50px;
    height: 50px;
    right: 1rem;
  }
}
footer #page_top a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  cursor: pointer;
  z-index: 11;
}
footer #page_top a:hover {
  opacity: 0.7;
}
footer #page_top a:before {
  content: "";
  width: 6px;
  height: 6px;
  border: 0;
  border-top: solid 3px #fff;
  border-right: solid 3px #fff;
  position: absolute;
  top: calc(50% - 3px);
  left: calc(50% - 5px);
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}
@media screen and (max-width: 767px) {
  footer #page_top a:before {
    width: 4px;
    height: 4px;
    border-top: solid 2px #fff;
    border-right: solid 2px #fff;
    top: calc(50% - 2px);
    left: calc(50% - 3px);
  }
}
footer .copyright {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 90px;
  font-size: 10px;
  font-weight: 700;
  text-align: center;
  padding: 1.5rem 2rem;
  color: #fff;
  background-color: #e60012;
}
@media screen and (max-width: 767px) {
  footer .copyright {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    padding: 6vw 4vw;
    font-size: 2.4vw;
  }
}
footer .copyright p {
  width: 1120px;
  max-width: 100%;
  margin: 0 auto;
  text-align: center;
}
footer nav {
  margin: 0 0 70px;
}
@media screen and (max-width: 767px) {
  footer nav {
    margin: 0;
  }
}
footer nav ul li a {
  display: inline-block;
  font-size: 13px;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  footer nav ul li a {
    position: relative;
    padding: 5px 1rem;
    font-size: 13px;
  }
}
footer nav ul li a:hover {
  color: #e60012;
}
footer nav ul li a[target=_blank]::after {
  content: "";
  display: inline-block;
  background: url("../img/common/ic_link_blank_white.svg") center/cover no-repeat;
  width: 14px;
  height: 14px;
  position: relative;
  top: 1px;
  margin-left: 6px;
}
footer nav .fNav_pc {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
footer nav .fNav_pc .fNav-1 {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: 30%;
  padding: 0 2rem 0 1rem;
}
footer nav .fNav_pc .fNav-1 .pct-logo {
  text-align: center;
}
footer nav .fNav_pc .fNav-1 .pct-logo a {
  display: inline-block;
}
footer nav .fNav_pc .fNav-1 .pct-logo a img {
  margin: 0 auto;
  width: 200px;
  height: auto;
}
footer nav .fNav_pc .fNav-1 .list-sns {
  margin: 2rem 0 0;
}
footer nav .fNav_pc .fNav-1 .list-sns ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
footer nav .fNav_pc .fNav-1 .list-sns ul li {
  width: 26px;
  height: 26px;
  margin: 0 0.5rem;
}
footer nav .fNav_pc .fNav-1 .list-sns ul li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
  margin: 0;
}
footer nav .fNav_pc .fNav-1 .list-sns ul li a img {
  max-width: 100%;
  height: auto;
}
footer nav .fNav_pc .fNav-2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 70%;
}
footer nav .fNav_pc .fNav-2 > li {
  width: 25%;
  margin: 0 1rem 0 0;
}
footer nav .fNav_pc > ul.fNav-2 > a {
  font-size: 20px;
}
footer nav .fNav_pc > ul.fNav-2 > p {
  font-size: 20px;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  footer nav .fNav_pc > ul.fNav-2 > p::before, footer nav .fNav_pc > ul.fNav-2 > p::after {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
  }
}
footer nav .fNav_pc > ul > li > a {
  border-bottom: 1px solid #fff;
  display: block;
  padding-bottom: 5px;
}
footer nav .fNav_pc > ul > li > p {
  border-bottom: 1px solid #fff;
  display: block;
  font-weight: 700;
}
footer nav .fNav_pc > ul > li > ul {
  padding: 10px 0 0.5rem;
}
footer nav .fNav_pc > ul > li > ul > li {
  font-size: 16px;
}
footer nav .fNav_pc > ul > li > ul > li ul li {
  margin-top: 3px;
}
footer nav .fNav_pc > ul > li > ul > li ul li a {
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  footer nav .fNav_pc > ul > li > ul a[target=_blank] {
    padding-right: 40px;
  }
}
footer nav .fNav_sp {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
footer nav .fNav_sp ul li a::after {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 0;
}
footer nav .fNav_sp .fNav-1 {
  width: 100%;
  padding: 2.7rem 1rem;
}
footer nav .fNav_sp .fNav-1 .pct-logo {
  text-align: center;
}
footer nav .fNav_sp .fNav-1 .pct-logo a {
  display: inline-block;
}
footer nav .fNav_sp .fNav-1 .pct-logo a img {
  margin: 0 auto;
  width: 50vw;
  max-width: 260px;
  height: auto;
}
footer nav .fNav_sp .fNav-1 .pct-logo span {
  display: block;
  width: 100%;
  margin: 0.2rem 0 0;
  text-align: center;
  font-size: 0.75rem;
  color: #fff;
}
footer nav .fNav_sp .fNav-1 .list-sns {
  margin: 1.5rem 0 0;
}
footer nav .fNav_sp .fNav-1 .list-sns ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
footer nav .fNav_sp .fNav-1 .list-sns ul li {
  width: 10vw;
  height: 10vw;
  max-height: 10vw;
  margin: 0 0.2rem;
  border: none;
}
footer nav .fNav_sp .fNav-1 .list-sns ul li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 24px;
  padding: 0;
  border: none;
}
footer nav .fNav_sp .fNav-1 .list-sns ul li a:after {
  display: none;
}
footer nav .fNav_sp ul > li {
  color: #fff;
}
footer nav .fNav_sp ul > li.nav-ttl {
  background-color: #f5f5f5;
}
footer nav .fNav_sp ul > li.nav-black {
  color: #444;
  background-color: #f5f5f5;
}
footer nav .fNav_sp ul > li.nav-black ul li:first-child {
  border-top: none;
}
footer nav .fNav_sp ul > li.nav-black ul a {
  min-height: 52px;
  border-bottom: 1px solid #d8d8d8;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-left: 8vw;
  font-weight: 400;
  color: #5d635e;
}
footer nav .fNav_sp ul > li.nav-black ul a:after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #aeaeae;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
footer nav .fNav_sp ul > li.nav-black ul a[target=_blank]:after {
  content: "";
  display: inline-block;
  background: url("../img/common/ic_link_blank_gray.svg") no-repeat;
  width: 14px;
  height: 14px;
  position: absolute;
  top: calc(50% - 7px);
}
footer nav .fNav_sp ul > li:first-child {
  border-top: 1px solid #d8d8d8;
}
footer nav .fNav_sp ul > li a {
  min-height: 52px;
  font-weight: 700;
  border-bottom: 1px solid #d8d8d8;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
}
footer nav .fNav_sp ul > li a:after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #aeaeae;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
footer nav .fNav_sp ul > li p {
  min-height: 52px;
  font-weight: 700;
  border-bottom: 1px solid #d8d8d8;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-left: 4vw;
  position: relative;
}
footer nav .fNav_sp ul > li p::before, footer nav .fNav_sp ul > li p::after {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
}
footer nav .fNav_sp ul > li p::before {
  width: 10px;
  height: 2px;
  background-color: #aeaeae;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 12px;
}
footer nav .fNav_sp ul > li p::after {
  width: 2px;
  height: 10px;
  background-color: #aeaeae;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 16px;
}
footer nav .fNav_sp ul > li p.close::after {
  display: none;
}
footer nav .fNav_sp ul > li p ul li a::after {
  width: 8px;
  height: 8px;
  border-top: #fff 2px solid;
  border-right: #fff 2px solid;
  -webkit-transform: rotate(45deg) translateY(-50%);
          transform: rotate(45deg) translateY(-50%);
  right: 22px;
}
footer nav .fNav_sp ul > li ul, footer nav .fNav_sp ul > li dl {
  display: none;
  color: #5d635e;
  background-color: #fff;
}
footer nav .fNav_sp ul > li dl a {
  color: #01a7ac;
  padding-left: 42px;
}
footer nav .fNav_sp ul > li dl a::after {
  display: none;
  padding-right: 0;
}
/*# sourceMappingURL=layout.css.map */