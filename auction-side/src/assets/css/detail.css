@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *共通パーツ
 * *********************************************************************** */
/* 前へ・次へボタン
 * *========================================== */
#item-detail .container {
  padding: 0 3rem 1rem;
}
@media screen and (max-width: 767px) {
  #item-detail .container {
    padding: 1rem;
  }
}

#main .item-name {
  display: grid;
  row-gap: 0.5rem;
  width: calc(1280px - 4rem);
  max-width: 100%;
  margin: 0 auto 1.2rem;
  padding: 0 0 1.2rem;
  border-bottom: 2px solid #eaeaea;
}
@media screen and (max-width: 767px) {
  #main .item-name {
    padding: 0 0 6vw;
    row-gap: 6vw;
  }
}
#main .item-name p {
  display: inline-block;
  font-size: 28px;
  font-weight: 500;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main .item-name p {
    font-size: 5.6vw;
  }
}
#main .item-name .tag_status {
  display: inline-block;
  margin: 0;
  padding: 0.5rem 0;
}
@media screen and (max-width: 767px) {
  #main .item-name .tag_status {
    margin: 0;
    padding: 0.5rem 0;
  }
}
#main .item-name .tag_status > p {
  display: inline-block;
  padding: 4px 12px 5px;
  font-size: 0.8rem;
  font-weight: 700;
  line-height: 1;
  border-radius: 20px;
}
@media screen and (max-width: 767px) {
  #main .item-name .tag_status > p {
    padding: 5px 16px 6px;
  }
}
#main .item-name .tag_status .status_recommend {
  color: #fff;
  background-color: #ff0000;
}
#main .item-name .tag_status .status_abc {
  color: #fff;
  background-color: #246ff9;
}
#main #terms {
  margin: 1rem 0 1rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
#main #terms p.tit-terms, #main #terms p.tit-period {
  color: #01a7ac;
  font-weight: 700;
  font-size: 18px;
  width: 80px;
  padding-top: 11px;
}
@media screen and (max-width: 767px) {
  #main #terms p.tit-terms {
    padding-top: 0;
    font-size: 17px;
  }
}
#main #terms .tag-terms {
  width: calc(100% - 80px);
  display: flex;
  flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  #main #terms .tag-terms {
    width: 100%;
  }
}
#main #terms .tag-terms a {
  display: inline-block;
  background-color: #EAEAEA;
  border-radius: 100vh;
  font-size: 16px;
  font-weight: 700;
  min-width: 100px;
  min-height: 30px;
  text-align: center;
  padding: 1px 20px;
  margin-left: 10px;
  margin-top: 10px;
}
@media screen and (max-width: 767px) {
  #main #terms .tag-terms a {
    margin-left: 0;
    margin-right: 5px;
    margin-top: 5px;
    font-size: 16px;
    min-width: 0;
  }
}
#main #terms p.tit-period {
  width: 90px;
  font-size: 15px;
  padding-top: 0;
  margin-top: 20px;
}
@media screen and (max-width: 767px) {
  #main #terms p.tit-period {
    font-size: 14px;
  }
}
#main #terms p.txt-period {
  width: calc(100% - 90px);
  font-size: 15px;
  margin-top: 20px;
}
@media screen and (max-width: 767px) {
  #main #terms p.txt-period {
    width: 100%;
    margin-top: 0;
    font-size: 14px;
  }
}
#main #terms p.txt-period span {
  font-weight: 700;
  display: inline-block;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *画像なしページ item-data-no-image
 * *********************************************************************** */
#main #item-data-no-image {
  padding: 40px 0 0;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image {
    padding: 0;
  }
}
#main #item-data-no-image .item-name {
  display: block;
  width: 100%;
  padding: 0 0 0.7rem;
}
#main #item-data-no-image .item-name p {
  font-size: 22px;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item-name p {
    font-size: 3.8vw;
  }
}
#main #item-data-no-image .item_main {
  width: 100%;
  margin: 40px 0 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main {
    flex-direction: column;
    margin: 0;
    gap: 8vw;
  }
}
#main #item-data-no-image .item_main .bid-field {
  width: 500px;
  max-width: 50%;
  min-width: 360px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  margin: 0 0 0 60px;
  padding: 1rem;
  border: 1px solid #ddd;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field {
    order: 1;
    width: 100%;
    max-width: 100%;
    min-width: 100%;
    margin: 0;
    padding: 0;
    border: none;
  }
}
#main #item-data-no-image .item_main .bid-field .auction-type {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0 0 1rem;
  padding: 0 0 0.9rem;
  color: #444;
  line-height: 1;
  border-bottom: 1px solid #ddd;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .auction-type {
    padding: 2vw 0;
  }
}
#main #item-data-no-image .item_main .bid-field .auction-type .title {
  font-size: 0.8rem;
}
#main #item-data-no-image .item_main .bid-field .auction-type .title .mark {
  display: inline-block;
  font-size: 1rem;
  font-weight: 600;
}
#main #item-data-no-image .item_main .bid-field .auction-type button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-end;
  width: 80px;
  padding: 0.5rem 0.7rem;
  color: #fff;
  line-height: 1;
  background-color: #e60012;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .auction-type button {
    width: auto;
    padding: 3vw 4vw;
  }
}
#main #item-data-no-image .item_main .bid-field .auction-type button img {
  width: 14px;
  height: auto;
  margin-right: 6px;
}
#main #item-data-no-image .item_main .bid-field .auction-type button span {
  display: inline-block;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .auction-type button span {
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data {
  width: 100%;
  margin: 0;
}
#main #item-data-no-image .item_main .bid-field .item-data dl {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 0;
  padding: 0;
}
#main #item-data-no-image .item_main .bid-field .item-data dl.num {
  margin: 0 0 1rem;
}
#main #item-data-no-image .item_main .bid-field .item-data dl.num span.amount {
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl.num span.amount {
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl.item-quantity {
  margin: 0 0 0.7rem;
  padding: 0 0 0.7rem;
  border-bottom: 1px solid #ddd;
}
#main #item-data-no-image .item_main .bid-field .item-data dl.ascending .start-price {
  font-size: 1.2rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl.ascending .start-price {
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl.ascending .start-price .unit {
  font-size: 1.2rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl.ascending .start-price .unit {
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl.ascending .price {
  color: #ff0000;
  font-size: 1.2rem;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl.ascending .price {
    font-size: 4.8vw;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl.ascending .price .unit {
  font-size: 1.2rem;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl.ascending .price .unit {
    font-size: 4.8vw;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl.status dd span.time {
  font-weight: 500;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl.status dd span.time {
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl.status dd span.remaining {
  margin-left: 0.3rem;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl.status dd span.remaining {
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl dt {
  position: relative;
  width: 40%;
  padding: 0 5px 0.2rem 0;
  margin: 0;
  font-weight: nomal;
  font-size: 1rem;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl dt {
    margin: 0;
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl dt:after {
  position: absolute;
  top: 0;
  right: 0;
  content: ":";
}
#main #item-data-no-image .item_main .bid-field .item-data dl dd {
  font-size: 1.2rem;
  font-weight: 500;
  width: 60%;
  margin: 0;
  padding: 0 0 0.2rem 10px;
  text-align: right;
  line-height: 1.1;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl dd {
    margin: 0;
    font-size: 3.8vw;
    font-weight: 500;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl dd span {
  display: inline-block;
}
#main #item-data-no-image .item_main .bid-field .item-data dl dd span.unit {
  font-size: 1.2rem;
  font-weight: 500;
  font-family: "sans-serif", "system-ui";
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl dd span.unit {
    font-size: 3.8vw;
    font-weight: 500;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl dd span.current-price {
  font-weight: 500;
}
#main #item-data-no-image .item_main .bid-field .item-data dl dd.price {
  font-size: 1.4rem;
  font-weight: 700;
  color: #ff0000;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data dl dd.price {
    font-size: 1.8rem;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data dl dd .size-up {
  font-size: 28px;
  font-weight: 500;
}
#main #item-data-no-image .item_main .bid-field .item-data.bid-history {
  margin: 1rem 0 0;
  padding: 0.5rem 1rem calc(0.5rem + 5px);
  font-size: 0.9rem;
  background-color: #f8f8f8;
}
#main #item-data-no-image .item_main .bid-field .item-data.bid-history dt {
  width: 50%;
  font-size: 0.8rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data.bid-history dt {
    width: 7rem;
  }
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data.bid-history dt.w_full {
    margin: 0.5rem 0 0;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data.bid-history dd {
  width: 50%;
  font-size: 0.8rem;
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data.bid-history dd {
    width: calc(100% - 7rem);
  }
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .item-data.bid-history dd.w_full {
    margin: 0.5rem 0 0;
  }
}
#main #item-data-no-image .item_main .bid-field .item-data span.current-p {
  font-weight: 700;
  display: inline-block;
}
#main #item-data-no-image .item_main .bid-field .item-data dt span.current-p {
  margin-top: 8px;
}
#main #item-data-no-image .item_main .bid-field .item-data dd span.current-p {
  font-size: 28px;
}
#main #item-data-no-image .item_main .bid-field .label-wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 0 -0.2rem;
}
#main #item-data-no-image .item_main .bid-field .label-wrap p {
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  height: auto;
  margin: 0 0.5rem 0.3rem 0;
  padding: 0.9rem 1.3rem 0.8rem;
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: 30px;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .label-wrap p {
    padding: 3.2vw 6.5vw 3vw;
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .label-wrap p span {
  position: relative;
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.2;
}
#main #item-data-no-image .item_main .bid-field .label-wrap p span:before {
  content: "";
  position: absolute;
  transform: translate(-50%, -50%);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
#main #item-data-no-image .item_main .bid-field .label-wrap p.top {
  color: #fff;
  border: 1px solid #f15813;
  background-color: #f15813;
}
#main #item-data-no-image .item_main .bid-field .label-wrap p.top span {
  padding: 0 0 0 29px;
}
#main #item-data-no-image .item_main .bid-field .label-wrap p.top span:before {
  top: 40%;
  left: 10px;
  width: 26px;
  height: 18px;
  background-image: url("../img/common/icn_crown_gold.svg");
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .label-wrap p.top span:before {
    top: 38%;
  }
}
#main #item-data-no-image .item_main .bid-field .label-wrap p.second {
  color: #fff;
  border: 1px solid #5a91ea;
  background-color: #5a91ea;
}
#main #item-data-no-image .item_main .bid-field .label-wrap p.second span {
  padding: 0 0 0 29px;
}
#main #item-data-no-image .item_main .bid-field .label-wrap p.second span:before {
  top: 40%;
  left: 10px;
  width: 26px;
  height: 18px;
  background-image: url("../img/common/icn_crown_silver.svg");
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .label-wrap p.second span:before {
    top: 38%;
  }
}
#main #item-data-no-image .item_main .bid-field .label-wrap p.reserve {
  color: #ef404e;
  border: 1px solid #f1858e;
}
#main #item-data-no-image .item_main .bid-field .label-wrap p.exceeding {
  color: #fff;
  background-color: #0ca393;
  border: 1px solid #0ca393;
}
#main #item-data-no-image .item_main .bid-field .place-bid {
  position: relative;
  width: 100%;
  margin: 1rem 0 1rem;
  padding: 1.2rem 1rem;
  background-color: #f9e2e4;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid {
    padding: 4vw;
    border-bottom: 1px solid #fff;
  }
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-amount {
  margin: 0;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price {
  margin: 0;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .price .unit {
  font-family: "sans-serif", "system-ui";
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount {
  display: grid;
  row-gap: 1rem;
  margin: 0 0 0.7rem;
  padding: 0;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price.ascending, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount.ascending {
  margin: 0;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .ttl, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .ttl {
  margin: 0;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .ttl, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .ttl {
    margin: 0;
  }
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .ttl span, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .ttl span {
  display: block;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .ttl span, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .ttl span {
    display: inline-block;
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .ttl .txt-error-w, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .ttl .txt-error-w {
  display: none;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .input-field, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .input-field {
  display: flex;
  align-items: center;
  font-size: 1.8rem;
  font-weight: bold;
  font-family: "Noto Sans";
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .input-field .unit, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .input-field .unit {
    font-size: 4.8vw;
  }
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .input-field input, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .input-field input {
  width: 14rem;
  max-width: 190px;
  margin: 0 0 0 0.2rem;
  padding: 5px;
  font-size: 1.8rem;
  font-weight: 500;
  text-align: right;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .input-field input::placeholder, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .input-field input::placeholder {
  color: #ddd;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid .bid-price .bid_head .input-field input, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount .bid_head .input-field input {
    width: 14rem;
    max-width: 36vw;
    height: 12vw;
    font-size: 4.8vw;
  }
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price ul, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount ul {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-end;
  margin: 0;
  font-family: "Noto Sans";
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price ul li, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount ul li {
  width: auto;
  margin: 0 0.3rem 0 0;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid .bid-price ul li, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount ul li {
    padding: 0 0.5rem 0.2rem 0;
  }
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price ul li + li, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount ul li + li {
  padding: 0 0 0 0.8rem;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid .bid-price ul li + li, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount ul li + li {
    padding: 0 0 0 0.5rem;
  }
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price ul > li > button, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount ul > li > button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0;
  padding: 0 0.8rem 0 0;
  font-size: 16px;
  background-color: #fff;
  border-radius: 4px;
  white-space: nowrap;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid .bid-price ul > li > button, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount ul > li > button {
    font-size: 3.8vw;
  }
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price ul > li > button span, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount ul > li > button span {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  margin: 2px 5px 2px 2px;
  padding: 0 7px;
  color: #fff;
  line-height: 1;
  background-color: #e60012;
  border-radius: 20px;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price ul > li > button span::after, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount ul > li > button span::after {
  content: "+";
  position: absolute;
  top: 1px;
  left: 5.5px;
  width: 14px;
  height: 14px;
  color: #fff;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price.error .bid_head .ttl .txt-error-w, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount.error .bid_head .ttl .txt-error-w {
  position: relative;
  display: block;
  margin: 0 0 0.2rem;
  color: #ff0000;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price.error .bid_head .ttl .txt-error-w:after, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount.error .bid_head .ttl .txt-error-w:after {
  position: absolute;
  display: inline-block;
  content: "";
  width: 14px;
  height: 14px;
  background: url("../img/common/icn_error.svg") no-repeat;
  background-size: contain;
}
#main #item-data-no-image .item_main .bid-field .place-bid .bid-price.error .bid_head .input-field input, #main #item-data-no-image .item_main .bid-field .place-bid .bid-amount.error .bid_head .input-field input {
  background-color: #fffac7;
  border: 1px solid #c4c381;
}
#main #item-data-no-image .item_main .bid-field .place-bid .total-bid-price {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2px solid #e5cacc;
}
#main #item-data-no-image .item_main .bid-field .place-bid .total-bid-price .price-label {
  font-weight: 500;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid .total-bid-price .price-label {
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .place-bid .total-bid-price .total-bid-amount {
  font-size: 1.8rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .place-bid .total-bid-price .total-bid-amount {
    font-size: 4.8vw;
  }
}
#main #item-data-no-image .item_main .bid-field .btn-bid-wrap {
  width: 100%;
}
#main #item-data-no-image .item_main .bid-field .btn-bid-wrap .button-bid {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 60px;
}
#main #item-data-no-image .item_main .bid-field .btn-bid-wrap .button-bid button.bid {
  width: calc(100% - 70px - 1rem);
  height: 100%;
  color: #fff;
  font-size: 1rem;
  font-weight: bold;
  background-color: #e60012;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .btn-bid-wrap .button-bid button.bid {
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .btn-bid-wrap .button-bid button.fav-mark {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: auto;
  margin: 0 0 0 1rem;
  padding: 2px 5px;
  text-align: center;
  border: 1px solid #ccc;
  background-color: #fff;
}
#main #item-data-no-image .item_main .bid-field .btn-bid-wrap .button-bid button.fav-mark .fav-pct {
  width: 30px;
  height: 30px;
  margin: 0 auto;
  padding: 2px;
  background: url("../img/common/icn_favorite_gr.svg");
  background-size: 28px auto;
  background-position: 50% 50%;
  background-clip: content-box;
}
#main #item-data-no-image .item_main .bid-field .btn-bid-wrap .button-bid button.fav-mark .fav-pct.added {
  background: url("../img/common/icn_favorite.svg");
  background-size: 28px auto;
  background-position: 50% 50%;
  background-clip: content-box;
}
#main #item-data-no-image .item_main .bid-field .btn-wrap-contact {
  width: 100%;
  margin: 1rem 0;
}
#main #item-data-no-image .item_main .bid-field .btn-wrap-contact .contact {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 52px;
  margin: 0;
  padding: 0;
  color: #444;
  font-size: 14.4px;
  font-weight: nomal;
  line-height: 1.1;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .btn-wrap-contact .contact {
    height: 60px;
    font-size: 3vw;
  }
}
#main #item-data-no-image .item_main .bid-field .btn-wrap-contact .contact::after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #ccc;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
#main #item-data-no-image .item_main .bid-field .list-pdf {
  padding: 1rem 0;
}
#main #item-data-no-image .item_main .bid-field .list-pdf ul li a {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  color: #0084c1;
  text-decoration: none;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .list-pdf ul li a {
    font-size: 3.5vw;
  }
}
#main #item-data-no-image .item_main .bid-field .list-pdf ul li a:hover .txt {
  text-decoration: underline;
}
#main #item-data-no-image .item_main .bid-field .list-pdf ul li a:hover .tab-pdf {
  color: #fff;
  background-color: #e60012;
  border: 1px solid #e60012;
  text-decoration: none;
}
#main #item-data-no-image .item_main .bid-field .list-pdf ul li a .txt {
  color: #0084c1;
  font-size: 0.9rem;
  line-height: 2;
}
#main #item-data-no-image .item_main .bid-field .list-pdf ul li a .tab-pdf {
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  margin: 0 0 0 0.3rem;
  padding: 0 0.5rem;
  color: #e60012;
  font-size: 0.8rem;
  font-weight: 600;
  font-family: "San serif";
  border: 2px solid #e60012;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .bid-field .list-pdf ul li a .tab-pdf {
    font-size: 3vw;
    border: 1px solid #e60012;
  }
}
#main #item-data-no-image .item_main .spec-field {
  flex: 1;
  margin: 0 0 40px;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .spec-field {
    order: 2;
    width: 100%;
    margin: 0 auto 20px;
  }
}
#main #item-data-no-image .item_main .spec-field .contents-wrap {
  padding: 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .spec-field .contents-wrap {
    padding: 0;
  }
}
#main #item-data-no-image .item_main .spec-field .contents-wrap table {
  width: 100%;
  margin: 0;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .spec-field .contents-wrap table {
    margin: 0;
  }
}
#main #item-data-no-image .item_main .spec-field .contents-wrap table tr th, #main #item-data-no-image .item_main .spec-field .contents-wrap table tr td {
  padding: 0.8rem 1rem;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .spec-field .contents-wrap table tr th, #main #item-data-no-image .item_main .spec-field .contents-wrap table tr td {
    padding: 4vw;
  }
}
#main #item-data-no-image .item_main .spec-field .contents-wrap table tr th {
  width: 30%;
  max-width: 180px;
  color: #000;
  font-size: 14.4px;
  font-weight: bold;
  text-align: center;
  border-bottom: 1px solid #e60012;
  vertical-align: middle;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .spec-field .contents-wrap table tr th {
    width: 30%;
    font-size: 3vw;
    text-align: left;
  }
}
#main #item-data-no-image .item_main .spec-field .contents-wrap table tr td {
  width: 70%;
  font-size: 14.4px;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main #item-data-no-image .item_main .spec-field .contents-wrap table tr td {
    width: 70%;
    font-size: 3vw;
  }
}
/*# sourceMappingURL=detail.css.map */
