@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *マイページ
 * *********************************************************************** */
/* h1 Title
 * *========================================== */
.mypage #main {
  padding: 0;
}
@media screen and (max-width: 767px) {
  .mypage #main {
    padding: 0;
  }
}
.mypage #main .ttl-mypage {
  width: 100%;
  margin: 0 0 40px;
  padding: 2rem 1rem 2.2rem;
  color: #444;
  background-color: #f5f5f5;
  background: -webkit-gradient(linear, left top, right top, from(rgb(230, 0, 19)), to(rgb(228, 218, 219)));
  background: linear-gradient(90deg, rgb(230, 0, 19), rgb(228, 218, 219));
  background: -webkit-gradient(linear, left top, right top, from(rgb(202, 191, 191)), to(rgb(243, 239, 239)));
  background: linear-gradient(90deg, rgb(202, 191, 191), rgb(243, 239, 239));
}
@media screen and (max-width: 767px) {
  .mypage #main .ttl-mypage {
    margin: 0 0 4vw;
    padding: 6vw 4vw;
  }
}
.mypage #main .ttl-mypage .ttl {
  width: 100%;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}
@media screen and (max-width: 767px) {
  .mypage #main .ttl-mypage .ttl {
    font-size: 4.8vw;
  }
}

/* Nav
 * *========================================== */
#main #mypage-head {
  padding: 0 1rem;
}
#main #mypage-head .nav-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  margin: 3rem 0 0;
  border-bottom: 4px solid #bb0000;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap {
    margin: 4vw 0 0;
    border-bottom: none;
    gap: 2px;
  }
}
#main #mypage-head .nav-wrap .nav-content {
  width: 25%;
  height: 60px;
  margin: 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap .nav-content {
    width: calc((100% - 0.5vw) / 4);
    height: 64px;
    margin: 0 0 0.5vw;
  }
}
#main #mypage-head .nav-wrap .nav-content a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  height: 100%;
  padding: 0;
}
#main #mypage-head .nav-wrap .nav-content a .label {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: calc(0.5rem + 2px) 0.5rem 0.5rem;
  font-weight: bold;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap .nav-content a .label {
    position: relative;
    padding: 32px 0.5vw 0.5vw;
    font-size: 3vw;
  }
}
#main #mypage-head .nav-wrap .nav-content a .label.favorite:before {
  content: url(../img/common/icn_mypage_nav_favorite.svg);
  display: inline-block;
  vertical-align: middle;
  padding: 2px 7px 0 0;
  width: 22px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap .nav-content a .label.favorite:before {
    position: absolute;
    top: 11px;
    left: calc(50% - 9px);
    width: 18px;
  }
}
#main #mypage-head .nav-wrap .nav-content a .label.bid:before {
  content: url(../img/common/icn_mypage_nav_bid.svg);
  display: inline-block;
  vertical-align: middle;
  padding: 4px 7px 0 0;
  width: 22px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap .nav-content a .label.bid:before {
    position: absolute;
    top: 8px;
    left: calc(50% - 9px);
    width: 16px;
  }
}
#main #mypage-head .nav-wrap .nav-content a .label.sbid:before {
  content: url(../img/common/icn_mypage_nav_sbid.svg);
  display: inline-block;
  vertical-align: middle;
  padding: 4px 7px 0 0;
  width: 22px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap .nav-content a .label.sbid:before {
    position: absolute;
    top: 8px;
    left: calc(50% - 9px);
    width: 16px;
  }
}
#main #mypage-head .nav-wrap .nav-content a .label.account:before {
  content: url(../img/common/icn_mypage_nav_account.svg);
  display: inline-block;
  vertical-align: middle;
  padding: 4px 7px 0 0;
  width: 22px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap .nav-content a .label.account:before {
    position: absolute;
    top: 8px;
    left: calc(50% - 9px);
    width: 16px;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap .nav-content:not(.active) a {
    border: 1px solid #ddd;
    border-radius: 4px;
  }
}
#main #mypage-head .nav-wrap .nav-content:not(.active) a:hover {
  color: #bb0000;
  opacity: 1;
}
#main #mypage-head .nav-wrap .nav-content:not(.active) a:hover .label.favorite:before {
  content: url(../img/common/icn_mypage_nav_favorite_dred.svg);
}
#main #mypage-head .nav-wrap .nav-content:not(.active) a:hover .label.bid:before {
  content: url(../img/common/icn_mypage_nav_bid_dred.svg);
}
#main #mypage-head .nav-wrap .nav-content:not(.active) a:hover .label.sbid:before {
  content: url(../img/common/icn_mypage_nav_sbid_dred.svg);
}
#main #mypage-head .nav-wrap .nav-content:not(.active) a:hover .label.account:before {
  content: url(../img/common/icn_mypage_nav_account_dred.svg);
}
#main #mypage-head .nav-wrap .nav-content.active a {
  cursor: default;
  background-color: #bb0000;
  border: 1px solid #bb0000;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap .nav-content.active a {
    border-radius: 4px;
    border: none;
  }
}
#main #mypage-head .nav-wrap .nav-content.active a:hover {
  opacity: 1;
}
#main #mypage-head .nav-wrap .nav-content.active .label {
  color: #fff;
  font-weight: bold;
}
#main #mypage-head .nav-wrap .nav-content.active .label.favorite:before {
  content: url(../img/common/icn_mypage_nav_favorite_wh.svg);
}
#main #mypage-head .nav-wrap .nav-content.active .label.bid:before {
  content: url(../img/common/icn_mypage_nav_bid_wh.svg);
}
#main #mypage-head .nav-wrap .nav-content.active .label.sbid:before {
  content: url(../img/common/icn_mypage_nav_sbid_wh.svg);
}
#main #mypage-head .nav-wrap .nav-content.active .label.account:before {
  content: url(../img/common/icn_mypage_nav_account_wh.svg);
}
#main #method-switch .container {
  width: 100%;
  padding: 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #method-switch .container {
    margin: 0;
    padding: 0;
  }
}
#main #method-switch .container .nav-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 1280px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0.6rem;
  background-color: #eee;
  border-bottom: 1px solid #ddd;
  border-radius: 0;
  gap: 0.4rem;
}
@media screen and (max-width: 767px) {
  #main #method-switch .container .nav-wrap {
    padding: 2vw 6vw 0;
    background-color: #fff;
    gap: 1vw;
  }
}
#main #method-switch .container .nav-wrap .nav-content-active {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 25%;
  padding: 0.8rem 1.5rem;
  color: #bb0000;
  text-align: center;
  line-height: 1.3;
  border-radius: 6px;
  background-color: #fff6f6;
  border: 1px solid #bb0000;
  -webkit-box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.2);
          box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.2);
}
@media screen and (max-width: 1080px) {
  #main #method-switch .container .nav-wrap .nav-content-active {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
@media screen and (max-width: 767px) {
  #main #method-switch .container .nav-wrap .nav-content-active {
    width: 50%;
    background-color: #fff;
    border: none;
    border-bottom: 3px solid #bb0000;
    border-radius: 0;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
}
#main #method-switch .container .nav-wrap .nav-content-active span {
  display: inline-block;
  font-weight: 600;
}
#main #method-switch .container .nav-wrap a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 25%;
  height: 100%;
  padding: 0.8rem 1.5rem;
  color: #000;
  text-align: center;
  line-height: 1.3;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  -webkit-tap-highlight-color: transparent;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
@media screen and (max-width: 1080px) {
  #main #method-switch .container .nav-wrap a {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
@media screen and (max-width: 767px) {
  #main #method-switch .container .nav-wrap a {
    width: 50%;
    border: none;
    border-radius: 0;
  }
}
#main #method-switch .container .nav-wrap a span {
  display: inline-block;
  font-weight: 600;
}
#main #method-switch .container .nav-wrap a:hover {
  color: #bb0000;
  opacity: 1;
}
#main #mypage-form {
  margin: 0 0 60px;
  padding: 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-form {
    margin: 0 0 40px;
    padding: 0 0 1rem;
  }
}
#main #mypage-form .container h3 {
  margin: 1rem 0 3rem;
  font-size: 1.8rem;
  font-weight: 700;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container h3 {
    margin: 1rem 0 2rem;
    font-size: 1.4rem;
  }
}
#main #mypage-form .container .btn-wrap-account {
  margin: 0 0 5rem;
}
#main #mypage-form .container .btn-wrap-account button.account {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 400px;
  max-width: calc(100% - 2rem);
  height: 56px;
  margin: 0 auto;
  padding: 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.1;
  background-color: #e60012;
  border: 2px solid #e60012;
  border-radius: 30px;
}
#main #mypage-form .container .btn-wrap-account button.account::after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #fff;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
#main #mypage-form .container .btn-wrap-account button.account span {
  display: block;
  width: 100%;
  font-weight: 500;
  text-align: center;
}
#main #mypage-form .container .btn-wrap-account button.account span.sub {
  margin: 6px 0 0;
  font-size: 0.7rem;
}
#main #mypage-form .container .btn-wrap-account p {
  margin: 0.5rem 0 0;
  font-size: 0.9rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .btn-wrap-account p {
    font-size: 0.7rem;
  }
}
#main #mypage-list {
  width: 100%;
  max-width: 100%;
  margin: 60px auto;
  padding: 0 1rem 30px;
}
@media screen and (max-width: 767px) {
  #main #mypage-list {
    margin: 40px auto;
  }
}
#main #mypage-list .container h3 {
  margin: 1rem 0 3rem;
  font-size: 1.8rem;
  font-weight: 700;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container h3 {
    margin: 1rem 0 2rem;
    font-size: 1.4rem;
  }
}
#main #mypage-list .container .matching-dir {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
}
#main #mypage-list .container .matching-dir > li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  margin: 0;
  border: 1px solid #e5e5e5;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li {
    margin: 0 0 1.5 rem;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main #mypage-list .container .matching-dir > li + li {
  margin: 2rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li + li {
    margin: 1.5rem 0 0;
  }
}
#main #mypage-list .container .matching-dir > li figure {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-right: 1px solid #e5e5e5;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct {
  width: 100%;
  height: auto;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct img {
  max-width: 360px;
  height: auto;
}
@media screen and (max-width: 1080px) {
  #main #mypage-list .container .matching-dir > li figure .wrap_pct img {
    max-width: 240px;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li figure .wrap_pct img {
    width: 100%;
    max-width: 100%;
    max-height: 100%;
    height: auto;
  }
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .status {
  position: absolute;
  bottom: 10px;
  left: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  height: auto;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .status > p {
  display: inline-block;
  width: auto;
  max-width: 110px;
  margin: 2px 0;
  padding: 2px 12px;
  font-size: 0.8rem;
  font-weight: 700;
  text-align: center;
  border-radius: 20px;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .status .status_top {
  color: #fff;
  background-color: #ff0000;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .status .status_overbid {
  color: #ff0000;
  background-color: #D3D1D0;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .status_soldout {
  position: absolute;
  top: 26%;
  left: 10%;
  width: 80%;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .status_soldout img {
  width: 100%;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .favorite {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 34px;
  height: 34px;
  padding: 2px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
  background-image: url(../img/common/icn_favorite.svg);
  background-size: 26px auto;
  background-repeat: no-repeat;
  background-position: 3px 2px;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .favorite::hover {
  opacity: 0.7;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .favorite img {
  width: 38px;
  height: auto;
}
#main #mypage-list .container .matching-dir > li figure .wrap_pct .favorite.active {
  background-image: url(../img/common/icn_favorite_on.svg);
}
#main #mypage-list .container .matching-dir > li .product-wrap {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: calc(100% - 360px) 360px;
  grid-template-columns: calc(100% - 360px) 360px;
  grid-auto-rows: minmax(70px, auto);
  width: calc(100% - 360px);
}
@media screen and (max-width: 1080px) {
  #main #mypage-list .container .matching-dir > li .product-wrap {
    -ms-grid-columns: calc(100% - 240px) 240px;
    grid-template-columns: calc(100% - 240px) 240px;
    width: calc(100% - 220px);
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    width: 100%;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .item-name {
  -ms-grid-row: 1;
  -ms-grid-row-span: 1;
  grid-row: 1/2;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
  grid-column: 1/2;
  position: relative;
  width: 100%;
  margin: 0;
  padding: 1rem 110px 1rem 1rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.3;
  background-color: #444;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .item-name {
    -ms-grid-row: 1;
    -ms-grid-row-span: 1;
    grid-row: 1/2;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    padding: 0.5rem 1rem 0.5rem 1rem;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .item-name .tag_status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  margin: 0;
  padding: 0;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .item-name .tag_status {
    position: static;
    display: block;
    width: 100%;
    padding: 0.5rem 0 0.3rem;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .item-name .tag_status > p {
  display: inline-block;
  margin: 0 2px 2px 0;
  padding: 4px 12px 5px;
  font-size: 0.8rem;
  font-weight: 700;
  line-height: 1;
  border-radius: 20px;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .item-name .tag_status > p {
    padding: 5px 16px 6px;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .item-name .tag_status .status_recommend {
  color: #fff;
  background-color: #ff0000;
}
#main #mypage-list .container .matching-dir > li .product-wrap .desc {
  -ms-grid-row: 2;
  -ms-grid-row-span: 1;
  grid-row: 2/3;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
  grid-column: 1/2;
  padding: 1rem 1rem 1rem 0.5rem;
  font-size: 0.8rem;
  background-color: #e5e5e5;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .desc {
    -ms-grid-row: 4;
    -ms-grid-row-span: 1;
    grid-row: 4/5;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    background-color: #fff;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .desc dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
#main #mypage-list .container .matching-dir > li .product-wrap .desc dl dt {
  width: 20%;
  padding: 0 0 0.3rem 0.5rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .desc dl dt {
    width: 40%;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .desc dl dd {
  width: 30%;
  padding: 0 0.5rem 0.3rem 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .desc dl dd {
    width: 60%;
    padding: 0 0 0.3rem 0.5rem;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell {
  -ms-grid-row: 3;
  -ms-grid-row-span: 1;
  grid-row: 3/4;
  -ms-grid-column: 1;
  -ms-grid-column-span: 1;
  grid-column: 1/2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  background-color: #fff;
}
@media screen and (max-width: 1200px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .sell {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .sell {
    -ms-grid-row: 2;
    -ms-grid-row-span: 1;
    grid-row: 2/3;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .price {
  width: calc(100% - 240px);
  padding: 0.5rem 1rem;
}
@media screen and (max-width: 1200px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .sell .price {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .sell .price {
    text-align: center;
    background-color: #f0f0f0;
    border-bottom: 1px solid #fff;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .price-v {
  display: inline-block;
  margin: 0 0.5rem;
  color: #E50A09;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 1.2;
  white-space: nowrap;
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status {
  width: 240px;
  margin: 0 auto;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status {
    width: 100%;
    background-color: #f0f0f0;
    border-bottom: 1px solid #fff;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .end-date {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0.5rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .end-date {
    padding: 1rem 0.5rem 0.7rem;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .end-date img {
  width: 23px;
  height: 23px;
  margin: 0 10px;
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .other-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 240px;
  margin: 0 auto;
  padding: 0 0.5rem 0.5rem;
  font-size: 0.8rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .other-info {
    width: 220px;
    max-width: 100%;
    padding: 0 0.5rem 1rem;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .other-info .view img {
  width: 30px;
  height: auto;
  margin: 7px 5px 0 0;
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .other-info .favorite img {
  width: 24px;
  height: auto;
  margin: 4px 5px 0 0;
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .other-info .bid img {
  width: 22px;
  height: auto;
  margin: 6px 5px 0 0;
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .other-info > div {
  width: 33.33%;
}
#main #mypage-list .container .matching-dir > li .product-wrap .sell .current-status .other-info > div > span {
  font-weight: 700;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid {
  -ms-grid-row: 1;
  -ms-grid-row-span: 3;
  grid-row: 1/4;
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  position: relative;
  width: 360px;
  padding: 1rem 1rem calc(46px + 2rem);
  background-color: #f0f0f0;
}
@media screen and (max-width: 1080px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid {
    width: 240px;
    max-width: 100%;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid {
    -ms-grid-row: 3;
    -ms-grid-row-span: 1;
    grid-row: 3/4;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    width: 100%;
    max-width: 100%;
    border-bottom: 1px solid #fff;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .ttl {
  margin: 0 0 0.5rem;
  font-size: 1.2rem;
  font-weight: 700;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price {
  width: 100%;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: right;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input {
  width: 18rem;
  max-width: calc(100% - 30px);
  margin: 0 0 0 0.5rem;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input {
    width: calc(100% - 30px);
    max-width: calc(100% - 30px);
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input::-webkit-input-placeholder {
  color: #ddd;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input::-moz-placeholder {
  color: #ddd;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input:-ms-input-placeholder {
  color: #ddd;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input::-ms-input-placeholder {
  color: #ddd;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input::placeholder {
  color: #ddd;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input.price-bid-comp {
  background-color: #e5e5e5;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input.price-bid-comp::-webkit-input-placeholder {
  color: #000;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input.price-bid-comp::-moz-placeholder {
  color: #000;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input.price-bid-comp:-ms-input-placeholder {
  color: #000;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input.price-bid-comp::-ms-input-placeholder {
  color: #000;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .price input.price-bid-comp::placeholder {
  color: #000;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 1rem 0 1rem;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid ul > li {
  width: calc(50% - 5px - 2px);
  margin: 0;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid ul > li:first-child {
  margin: 0 14px 0 0;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid ul > li > button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
  margin: 0 5px 5px 0;
  padding: 0 10px 0 0;
  font-size: 1rem;
  background-color: #fff;
  border: 1px solid #CDCBCA;
  border-radius: 30px;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid ul > li > button {
    font-size: 1rem;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid ul > li > button span {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  margin: 2px 5px 2px 2px;
  padding: 0 7px;
  color: #fff;
  line-height: 1;
  background-color: #e60012;
  border-radius: 20px;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid ul > li > button span::after {
  content: "+";
  position: absolute;
  top: 0.7px;
  left: 5.5px;
  width: 14px;
  height: 14px;
  color: #fff;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .payment button {
  position: relative;
  width: calc(100% - 50px - 1rem);
  height: 55px;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #e60012;
  border-radius: 4px;
  line-height: 1.2;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .payment button.payment {
  width: 100%;
  margin: 1rem 0 0;
  padding: 0;
  text-align: center;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .payment button:after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #fff;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid.receipt {
  margin: 0.5rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid.receipt {
    margin: 0.5rem 0 0;
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid button {
  width: calc(100% - 50px - 1rem);
  height: 55px;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #e60012;
  border-radius: 4px;
  line-height: 1.2;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid button.receipt {
  width: 100%;
  padding: 1px 25% 3px 10px;
  text-align: right;
  background-image: url(../img/common/icn_download_list.svg);
  background-repeat: no-repeat;
  background-position: right 10% top 50%;
}
@media screen and (max-width: 1080px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid button.receipt {
    text-align: center;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid button.receipt {
    padding: 1px 10px 3px 10px;
    text-align: center;
    background-position: right calc(50% - 7.2rem) top 50%;
  }
}
@media screen and (max-width: 400px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid button.receipt {
    padding: 1px 46px 3px 10px;
    background-position: right 6% top 50%;
  }
}
@media screen and (max-width: 1080px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid button {
    width: auto;
    min-width: calc(100% - 55px - 1rem);
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid button {
    width: calc(100% - 55px - 0.5rem);
  }
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid .update {
  position: relative;
  width: 55px;
  height: 55px;
  margin: 0 0 0 1rem;
  padding: 1.5rem 0 0;
  color: #e60012;
  text-align: center;
  background-color: #fff;
  border: 1px solid #CDCBCA;
  border-radius: 30px;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid .update span {
  font-size: 0.8rem;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .button-bid .update::after {
  content: "";
  display: inline-block;
  background: url("../img/common/icn_update_list.svg") center 8px no-repeat;
  background-size: 20px auto;
  width: 30px;
  height: 30px;
  position: absolute;
  top: 0;
  left: calc(50% - 15px);
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .other-info-detail {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  width: calc(100% - 2rem);
  z-index: 10;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .other-info-detail button {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  height: 46px;
  margin: 0;
  padding: 0;
  color: #e60012;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.1;
  background-color: #fff;
  border: 2px solid #e60012;
  border-radius: 30px;
}
#main #mypage-list .container .matching-dir > li .product-wrap .place-bid .other-info-detail button::after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #e60012;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
#main #mypage-list .container .matching-dir > li.soldout figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: -webkit-fill-available;
  padding: 0;
  background-image: url(../img/common/icn_soldout.png);
  background-size: 82% auto;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .matching-dir > li.soldout figure:after {
    background-size: 72% auto;
  }
}
#main #mypage-list .container .matching-dir > li.soldout .product-wrap .place-bid:after {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.5);
  z-index: 2;
}
#main #mypage-list .container .payment-method {
  margin: 1rem auto 0;
  padding: 2rem 3rem;
  background-color: #f0f0f0;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method {
    padding: 1rem;
  }
}
#main #mypage-list .container .payment-method .payment-option {
  width: 100%;
  margin: 1rem 0;
  padding: 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #444;
}
#main #mypage-list .container .payment-method .payment-option:hover {
  background-color: #586154;
}
#main #mypage-list .container .payment-method .payment-option label {
  display: block;
  width: 100%;
  height: 100%;
  padding: 1.3rem 2rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .payment-option label {
    font-size: 0.9rem;
    padding: 1rem 1.2rem;
  }
}
#main #mypage-list .container .payment-method .payment-option label input {
  display: inline-block;
  margin: 0 1rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .payment-option label input {
    margin: 0 0.5rem 0 0;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap {
  width: 800px;
  max-width: calc(100% - 2rem);
  margin: 2rem auto 5rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap {
    width: 100%;
    max-width: 100%;
    margin: 1rem 0 3rem;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap h4 {
  margin: 1rem 0;
  padding: 0 0 0.2rem;
  font-weight: 700;
  font-size: 1.2rem;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap h4 {
    padding: 0 0 0.2rem;
    font-size: 1.1rem;
    border-bottom: 1px solid #ccc;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient {
  width: 100%;
  margin: 0 0 3rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient {
    margin: 0 0 1rem;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr {
  padding: 0.3rem 0;
}
#main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr th {
  font-size: 1rem;
  font-weight: 700;
  vertical-align: middle;
  width: 180px;
  position: relative;
  padding: 0 2rem;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr th {
    display: block;
    width: 100%;
    padding: 0;
    font-size: 0.9rem;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr th em.req {
  position: absolute;
  top: 50%;
  right: 10px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  display: inline-block;
  background-color: #E80000;
  width: 35px;
  height: 20px;
  color: #fff;
  font-weight: 700;
  font-size: 12px;
  text-align: center;
  line-height: 19px;
}
#main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr td {
  width: calc(100% - 1rem);
  padding: 15px 0;
  position: relative;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr td {
    display: block;
    width: 100%;
    padding: 10px 0 20px;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr td input.zip-search {
  margin: 5px 0 5px 15px;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr td input.zip-search {
    width: 80px;
    padding-left: 5px;
    padding-right: 5px;
    margin: 0 0 0 15px;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr input.iptW-S {
    width: calc(100% - 80px - 1rem);
  }
}
#main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr input.iptW-M {
  width: calc(100% - 1.5rem);
  max-width: 100%;
}
@media screen and (max-width: 1080px) {
  #main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr input.iptW-M {
    width: 360px;
  }
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .tbl-invoice-recipient tr input.iptW-M {
    width: 100%;
    max-width: 100%;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap .transfer-details {
  padding: 1rem 3rem 1.5rem;
  background-color: #f7f7f7;
  border: 1px solid #d1d1d1;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .transfer-details {
    padding: 1rem 1rem 1.5rem;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap .transfer-details h5 {
  margin: 0.5rem 0;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .transfer-details h5 {
    margin: 0 0 0.5rem;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap .transfer-details dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  width: 100%;
  font-size: 0.9rem;
}
#main #mypage-list .container .payment-method .invoice-wrap .transfer-details dl dt {
  width: 110px;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .transfer-details dl dt {
    width: 80px;
  }
}
#main #mypage-list .container .payment-method .invoice-wrap .transfer-details dl dd {
  width: calc(100% - 110px);
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .invoice-wrap .transfer-details dl dd {
    width: calc(100% - 80px);
  }
}
#main #mypage-list .container .payment-method .card-selection {
  width: 800px;
  max-width: calc(100% - 2rem);
  margin: 3rem auto 5rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection {
    max-width: 100%;
    margin: 2rem auto 2rem;
  }
}
#main #mypage-list .container .payment-method .card-selection .btn-wrap {
  margin: 1rem 0;
  text-align: right;
}
#main #mypage-list .container .payment-method .card-selection .btn-wrap button {
  padding: 0.5rem 1rem;
  color: #e60012;
  font-size: 1rem;
  line-height: 1.1;
  background-color: #fff;
  border: 1px solid #e60012;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .btn-wrap button {
    font-size: 0.8rem;
  }
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .head {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0.5rem 1rem;
  color: #fff;
  font-weight: 500;
  background-color: #444;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .card-select-panel .head {
    font-size: 0.8rem;
    background-color: #52594f;
  }
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .head .use {
  width: 30%;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .card-select-panel .head .use {
    width: 100%;
  }
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .head .num {
  width: 70%;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .card-select-panel .head .num {
    display: none;
  }
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .head .num span {
  display: inline-block;
  width: 50%;
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 5px 0 0;
  padding: 0.8rem 1rem;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card {
    padding: 0.8rem;
  }
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card:hover {
  background-color: #fcfbfb;
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card input {
  display: inline-block;
  margin: 0 calc(30% - 40px) 0 20px;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card input {
    margin: 0 25px 0 10px;
  }
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card input[type=radio]:checked + label {
  border: 1px solid #eaf1fa;
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card dl {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card dl dt {
  width: 50%;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card dl dt {
    width: 100%;
  }
}
#main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card dl dd {
  width: 50%;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .card-select-panel .body .card dl dd {
    width: 100%;
  }
}
#main #mypage-list .container .payment-method .card-selection .error-message {
  margin: 3rem 0;
  padding: 1.5rem 2rem;
  color: #e60012;
  background-color: #FFF2F2;
  border: 1px solid #FFC4C4;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .payment-method .card-selection .error-message {
    padding: 1rem;
    font-size: 0.8rem;
  }
}
#main #mypage-list .container .wrap-btn {
  width: 100%;
  margin: 3rem 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .wrap-btn {
    margin: 2rem 0 0;
  }
}
#main #mypage-list .container .wrap-btn .list-more {
  width: 300px;
  height: 55px;
  margin: 0 auto;
  padding: 0.5rem 2rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  background-color: #bf2a24;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #mypage-list .container .wrap-btn .list-more {
    height: 60px;
  }
}
#main #mypage-list .container .wrap-btn .btn.payment {
  color: #fff;
  font-weight: 700;
  background-color: #e60012;
  border-radius: 4px;
}
#main #mypage-list.payment .container .matching-dir > li .product-wrap .place-bid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 1rem;
}
#main #mypage-list.payment .container .matching-dir > li .product-wrap .place-bid .ttl {
  width: 100%;
}
#main #mypage-list.payment .container .matching-dir > li .product-wrap .place-bid .price {
  margin: 0 0 2rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-list.payment .container .matching-dir > li .product-wrap .place-bid .price {
    margin: 0 0 1rem;
  }
}
#main #mypage-list.payment .container .matching-dir > li .product-wrap .place-bid .other-info-detail {
  position: static;
}
/*# sourceMappingURL=mypage.css.map */
