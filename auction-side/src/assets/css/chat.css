@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *チャット
 * *********************************************************************** */
/* *========================================== */
#chat .container {
  width: 1280px;
  max-width: calc(100% - 2rem);
  margin: 1rem auto 3rem;
  padding: 0;
  position: relative;
}
#chat .container .tab-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
}
#chat .container .tab-wrap button {
  width: 50%;
  padding: 1.3rem 1rem;
}
@media screen and (max-width: 767px) {
  #chat .container .tab-wrap button {
    padding: 1rem 0.7rem;
  }
}
#chat .container .tab-wrap button span {
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #chat .container .tab-wrap button span {
    font-size: 1rem;
  }
}
#chat .container .tab-wrap button.is-active {
  color: #fff;
  background-color: #e60012;
}
#chat .container .tab-wrap button.is-active:hover {
  cursor: default;
  background-color: #e60012;
}
#chat .container .tab-wrap button:hover {
  background-color: #c7e3c1;
  opacity: 1;
}
#chat .container .chat-shipping {
  display: none;
}
#chat .container .chat-item, #chat .container .chat-shipping {
  position: relative;
  width: 100%;
  margin: 0 auto 40px;
  background-color: #f7f7f7;
  border: 3px solid #e60012;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item, #chat .container .chat-shipping {
    margin: 0 auto 20px;
  }
}
#chat .container .chat-item h3, #chat .container .chat-shipping h3 {
  margin: 2rem 0 1rem;
  color: #e60012;
  font-size: 1.8rem;
  font-weight: 700;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item h3, #chat .container .chat-shipping h3 {
    margin: 1rem 0 1rem;
    font-size: 1.4rem;
  }
}
#chat .container .chat-item .chat-head, #chat .container .chat-shipping .chat-head {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
  margin: 0;
  padding: 40px 40px 40px;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-head, #chat .container .chat-shipping .chat-head {
    padding: 5px 10px;
  }
}
#chat .container .chat-item .chat-head .chat-head-text, #chat .container .chat-shipping .chat-head .chat-head-text {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: calc(100% - 180px);
  padding-right: 20px;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-head .chat-head-text, #chat .container .chat-shipping .chat-head .chat-head-text {
    width: 100%;
    margin: 0 0 1rem;
    padding-right: 0;
  }
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-head .chat-head-text .chat-head-text-list, #chat .container .chat-shipping .chat-head .chat-head-text .chat-head-text-list {
    width: 100%;
    margin-right: 0;
    margin: 0;
  }
}
#chat .container .chat-item .chat-head .chat-head-text .chat-head-text-list:last-of-type, #chat .container .chat-shipping .chat-head .chat-head-text .chat-head-text-list:last-of-type {
  margin-right: 0;
}
#chat .container .chat-item .chat-head .chat-head-text .chat-head-text-list p, #chat .container .chat-shipping .chat-head .chat-head-text .chat-head-text-list p {
  width: 100%;
}
#chat .container .chat-item .chat-head .chat-head-text .chat-head-text-list p span, #chat .container .chat-shipping .chat-head .chat-head-text .chat-head-text-list p span {
  display: inline-block;
  margin-right: 1em;
  color: #000;
  font-size: 1.2rem;
  font-weight: bold;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-head .chat-head-text .chat-head-text-list p span, #chat .container .chat-shipping .chat-head .chat-head-text .chat-head-text-list p span {
    display: block;
    margin: 0;
    font-size: 1rem;
  }
}
#chat .container .chat-item .chat-head .chat-head-text .chat-head-text-list p span.label, #chat .container .chat-shipping .chat-head .chat-head-text .chat-head-text-list p span.label {
  width: 70px;
  color: #e60012;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-head .chat-head-text .chat-head-text-list p span.label, #chat .container .chat-shipping .chat-head .chat-head-text .chat-head-text-list p span.label {
    width: 80px;
    margin: 0 0 0.5rem;
  }
}
#chat .container .chat-item .chat-head .chat-head-btnWrap, #chat .container .chat-shipping .chat-head .chat-head-btnWrap {
  width: 180px;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-head .chat-head-btnWrap, #chat .container .chat-shipping .chat-head .chat-head-btnWrap {
    width: 100%;
  }
}
#chat .container .chat-item .chat-head .chat-head-btnWrap button, #chat .container .chat-shipping .chat-head .chat-head-btnWrap button {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: block;
  position: relative;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0.8rem 1rem;
  border-radius: 30px;
  color: #e60012;
  font-weight: 600;
  font-size: 1rem;
  text-align: center;
  line-height: 1.1;
  background-color: #fff;
  border: 2px solid #e60012;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-head .chat-head-btnWrap button, #chat .container .chat-shipping .chat-head .chat-head-btnWrap button {
    max-width: 100%;
    width: 280px;
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
  }
}
#chat .container .chat-item .chat-body, #chat .container .chat-shipping .chat-body {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  margin: 0 auto;
  padding: 10px 40px 40px;
  background: #F7F7F7;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-body, #chat .container .chat-shipping .chat-body {
    margin: 1rem auto;
    padding: 1rem;
  }
}
#chat .container .chat-item .chat-body .chat-body-wrap, #chat .container .chat-shipping .chat-body .chat-body-wrap {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  max-height: 1090px;
  padding: 3rem 4rem;
  overflow-y: scroll;
  background: #fff;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-body .chat-body-wrap, #chat .container .chat-shipping .chat-body .chat-body-wrap {
    max-height: 800px;
    padding: 1.5rem 1rem;
  }
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail {
  margin: 15px 0 0;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail.question_detail, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail.question_detail {
  margin-top: 50px;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail.question_detail:first-of-type, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail.question_detail:first-of-type {
  margin-top: 0;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail:first-of-type, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail:first-of-type {
  margin-top: 0;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-q, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-q {
  display: block;
  color: #000;
  font-weight: bold;
  font-size: 18px;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-q, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-q {
    font-size: 0.9rem;
  }
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  margin-top: 12px;
  padding: 25px 30px;
  border-radius: 10px;
  font-size: 16px;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text {
    padding: 15px;
    font-size: 0.9rem;
  }
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.user, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.user {
  background: #f0f5f0;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.seller, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-text.seller {
  background: rgba(255, 208, 170, 0.2);
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-a, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-a {
  display: block;
  color: #f67a54;
  font-weight: bold;
  font-size: 18px;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-a, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-a {
    font-size: 0.9rem;
  }
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip {
  position: relative;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-ico, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-ico {
  width: 20px;
  height: 20px;
  margin-top: 10px;
  margin-right: 30px;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-ico:hover, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-ico:hover {
  cursor: pointer;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-txt, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-txt {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: block;
  z-index: 1;
  position: absolute;
  bottom: -40px;
  left: calc(50% - 15px);
  width: 80px;
  padding: 7px 12px;
  -webkit-transform: translate(-50%, 0);
          transform: translate(-50%, 0);
  border-radius: 5px;
  background: #DDDDDD;
  color: #000;
  font-size: 0.8rem;
  line-height: 1;
  text-align: center;
  opacity: 0;
  -webkit-transition: all ease 0.3s;
  transition: all ease 0.3s;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-txt.is-show, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-txt.is-show {
  opacity: 1;
  -webkit-transition: all ease 0.7s;
  transition: all ease 0.7s;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-txt:hover, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-txt:hover {
  cursor: default;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-txt:after, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap .chat-tooltip .chat-tooltip-txt:after {
  display: inline-block;
  position: absolute;
  top: -10px;
  left: calc(50% - 10px);
  width: 0;
  height: 0;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #ddd;
  border-left: 10px solid transparent;
  content: "";
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: block;
  position: relative;
  width: 100%;
  max-width: 160px;
  margin: 10px 0 0;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  color: #fff;
  font-weight: bold;
  font-size: 1rem;
  text-align: center;
  background-color: #e60012;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button:hover, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button:hover {
  cursor: pointer;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button.normal, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button.normal {
  max-width: 240px;
  padding: 10px 20px;
  border-radius: 5px;
  background: #364A81;
  font-size: 1rem;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button.normal:after, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button.normal:after {
  display: none;
}
#chat .container .chat-item .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button:after, #chat .container .chat-shipping .chat-body .chat-body-wrap .chat-body-detail .chat-body-detail-btnWrap button:after {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 20px;
  width: 28px;
  height: 12px;
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  background: url(../../img/common/ic_btn_arrow.svg) no-repeat;
  background-size: 28px 12px;
  content: "";
  display: none;
}
#chat .container .chat-item .chat-foot, #chat .container .chat-shipping .chat-foot {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 100%;
  padding: 45px 40px 45px;
  background: #F7F7F7;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-foot, #chat .container .chat-shipping .chat-foot {
    padding: 20px 10px 20px;
  }
}
#chat .container .chat-item .chat-foot .chat-form, #chat .container .chat-shipping .chat-foot .chat-form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-foot .chat-form, #chat .container .chat-shipping .chat-foot .chat-form {
    padding-top: 10px;
  }
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-textarea, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-textarea {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: calc(100% - 150px);
  padding-right: 32px;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-foot .chat-form .chat-form-textarea, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-textarea {
    width: 100%;
    padding-right: 0;
  }
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-textarea textarea, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-textarea textarea {
  -webkit-appearance: none;
  width: 100%;
  min-height: 110px;
  padding: 11px 20px;
  border: 1px solid #e4e4e4;
  color: inherit;
  font-size: 1rem;
  resize: none;
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap {
  width: 150px;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap {
    width: 100%;
    margin: 10px 0 0;
  }
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  max-width: 280px;
  height: 45px;
  margin: 10px auto 0;
  border-radius: 4px;
  color: #fff;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.2;
  text-align: center;
  background-color: #e60012;
  -webkit-transition: all ease 0.7s;
  transition: all ease 0.7s;
}
@media screen and (max-width: 767px) {
  #chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn {
    width: 100%;
    max-width: 280px;
    height: 55px;
    margin: 10px auto 0;
  }
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn:hover, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn:hover {
  opacity: 0.7;
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.keep, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.keep {
  background: #364A81;
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.report, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.report {
  background: #E70012;
  color: #fff;
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.reset, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.reset {
  background: #666666;
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.submit, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.submit {
  background: #E04910;
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.lt_gray, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn.lt_gray {
  background: #999999 !important;
}
#chat .container .chat-item .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn:first-of-type, #chat .container .chat-shipping .chat-foot .chat-form .chat-form-btnWrap .chat-form-btn:first-of-type {
  margin-top: 0;
}
/*# sourceMappingURL=chat.css.map */