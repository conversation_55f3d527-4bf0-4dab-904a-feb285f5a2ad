@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *共通パーツ
 * *********************************************************************** */
#static {
  padding: 3rem 1rem;
  font-size: 16px;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #static {
    padding: 30px 4vw;
    font-size: 3.5vw;
  }
}
#static .container {
  padding: 0 0 3rem;
}
@media screen and (max-width: 767px) {
  #static .container {
    padding: 0 0 30px;
  }
}
#static h3.ttl-kobutsu {
  margin: 3rem auto 1.5rem;
  font-weight: 500;
  text-align: center;
}
#static a {
  color: #e60012;
}
#static a:hover {
  text-decoration: underline;
}
#static a.external::after {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-left: 6px;
  background-image: url("../img/common/icn_external.svg");
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;
  top: 1px;
}
@media screen and (max-width: 767px) {
  #static a.external::after {
    top: 0.5vw;
    width: 4vw;
    height: 4vw;
  }
}
#static .sign {
  width: 100%;
  margin: 1rem 0 0;
  text-align: right;
  line-height: 1.6;
}
@media screen and (max-width: 767px) {
  #static .sign {
    margin: 4vw 0 0;
  }
}
#static .pre {
  margin: 0 0 3.5rem;
}
@media screen and (max-width: 767px) {
  #static .pre {
    margin: 0 0 2rem;
  }
}

/* table
 * *========================================== */
#main table.tbl-otherItem {
  width: 100%;
  margin: 0;
  border-top: 1px solid #eaeaea;
}
@media screen and (max-width: 767px) {
  #main table.tbl-otherItem {
    border: none;
  }
}
#main table.tbl-otherItem th, #main table.tbl-otherItem td {
  border-bottom: 1px solid #eaeaea;
  vertical-align: top;
  font-size: 18px;
}
@media screen and (max-width: 767px) {
  #main table.tbl-otherItem th, #main table.tbl-otherItem td {
    width: 100%;
    display: block;
    padding: 15px 10px;
    font-size: 1rem;
    border: none;
  }
}
#main table.tbl-otherItem th {
  font-weight: 700;
  background-color: #F7F7F7;
}
#main table.tbl-otherItem td ul li {
  line-height: 1.6;
}
#main table.tbl-otherItem td ul li + li {
  margin: 0.8rem 0 0;
}
@media screen and (max-width: 767px) {
  #main table.tbl-otherItem td ul li + li {
    margin: 0.5rem 0 0;
  }
}
#main table.tbl-otherItem td ul.decimal {
  list-style-type: decimal;
  padding: 0 0 0 1.3rem;
}
#main table.tbl-otherItem td ul.disc {
  list-style-type: disc;
  padding: 0 0 0 1.3rem;
}
#main table.tbl-otherItem td dl {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 0.8rem 0 0;
  line-height: 1.6;
}
#main table.tbl-otherItem td dl dt {
  width: 5rem;
  margin: 0 0 0.8rem;
}
#main table.tbl-otherItem td dl dd {
  width: calc(100% - 5rem);
  margin: 0 0 0.8rem;
}
#main table.tbl-otherItem td dl.w-grad-equal dt {
  width: 8rem;
}
#main table.tbl-otherItem td dl.w-grad-equal dd {
  width: calc(100% - 8rem);
}
#main table.tbl-otherItem td ul {
  margin: 0.8rem 0 0;
}
#main table.tbl-otherItem td p {
  margin: 0.8rem 0 0;
  line-height: 1.6;
}
#main table.tbl-otherItem td .ma-0 {
  margin: 0;
}
#main table.tbl-otherItem td .ma-l {
  margin: 2.6rem 0 0;
}
#main table.tbl-otherItem td .ttl {
  font-weight: 500;
}

/* リスト
 * *========================================== */
/* ---------------------------
 * *introduction
 * *---------------------------- */
#main #static .introduction p {
  margin: 0;
  line-height: 1.6;
}
#main #static .introduction p.mb {
  margin: 0 0 1.3rem;
}
#main #static .introduction p.ttl {
  margin: 0 0 0.5rem;
  font-size: 22px;
  font-weight: 500;
  line-height: 1.3;
}
@media screen and (max-width: 767px) {
  #main #static .introduction p.ttl {
    margin: 0 0 0.5rem;
    font-size: 4.8vw;
  }
}
#main #static .introduction ol {
  margin: 0.3rem 0 1.3rem;
  padding: 0 0 0 1.2rem;
}
#main #static .introduction ol li {
  line-height: 1.6;
}

/* ---------------------------
 * *DL リスト（プライバシーポリシー／利用規約）
 * *---------------------------- */
dl.list_main .chapter {
  margin: 3.5rem 0 0.5rem;
  font-size: 1.6rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  dl.list_main .chapter {
    margin: 2rem 0 0.5rem;
    font-size: 5.2vw;
  }
}
dl.list_main .chapter span {
  display: inline-block;
  margin: 0 0.5rem 0 0;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  dl.list_main .chapter span {
    margin: 0 2vw 0 0;
  }
}
dl.list_main > dt {
  margin: 3.5rem 0 0.5rem;
  font-weight: 500;
  font-size: 22px;
  line-height: 1.3;
}
@media screen and (max-width: 767px) {
  dl.list_main > dt {
    margin: 2rem 0 0.5rem;
    font-size: 4.8vw;
  }
}
dl.list_main > dt.mt0 {
  margin: 0 0 0.5rem;
}
@media screen and (max-width: 767px) {
  dl.list_main > dt.mt0 {
    margin: 0 0 0.3rem;
  }
}
dl.list_main > dd p {
  line-height: 1.6;
}
dl.list_main > dd p.mb {
  margin: 0 0 1.3rem;
}
dl.list_main > dd p.mn {
  margin: 0;
}

ol.list_indent {
  list-style: none;
  padding-left: 1.1rem;
}
ol.list_indent li {
  padding: 0;
  text-indent: -1.1rem;
  line-height: 1.6;
}

ol.list_nmb-o1 {
  list-style: disc;
  padding: 0 0 0 2.2rem;
}
@media screen and (max-width: 767px) {
  ol.list_nmb-o1 {
    padding: 0 0 0 1.3rem;
  }
}
ol.list_nmb-o1 li {
  line-height: 1.6;
}
@media screen and (max-width: 767px) {
  ol.list_nmb-o1 li {
    line-height: 1.6;
  }
}
ol.list_nmb-o1.paragraph {
  margin: 0.5rem 0 0.5rem 1.5rem;
}

ol.list_nmb {
  counter-reset: cnt;
}
ol.list_nmb > li {
  list-style-type: none;
  counter-increment: cnt;
  padding: 0 0 0 1.7rem;
  text-indent: -1.7rem;
  line-height: 1.6;
}
ol.list_nmb > li.indent-lg {
  padding: 0 0 0 2rem;
  text-indent: -2rem;
}
ol.list_nmb > li.indent-md {
  padding: 0 0 0 1.7rem;
  text-indent: -1.7rem;
}
ol.list_nmb > li:before {
  content: "（" counter(cnt) "）";
}

ol.list_only_nmb {
  counter-reset: cnt;
}
ol.list_only_nmb > li {
  list-style-type: none;
  counter-increment: cnt;
  padding: 0 0 0 1.5rem;
  line-height: 1.6;
  text-indent: -1.5rem;
}
ol.list_only_nmb > li:before {
  content: "" counter(cnt) ".";
  margin-right: 0.5rem;
}

ol.list_no_indent {
  list-style: none;
  padding: 0 0 0 1.7rem;
}
@media screen and (max-width: 767px) {
  ol.list_no_indent {
    padding: 0 0 0 1.3rem;
  }
}
ol.list_no_indent li {
  line-height: 1.6;
}

/* ---------------------------
 * *UL リスト - 1
 * *----------------------------- */
ul.list_nmb-u1 li {
  text-indent: -2.15em;
  padding-left: 2.15em;
}
ul.list_nmb-u1 li span {
  display: inline-block;
  margin-right: 1em;
}

@media only screen and (max-width: 767px) {
  ul.list_nmb-u1 li + li {
    margin-top: 7px;
  }
}
/* ---------------------------
 * *リスト内コンテンツBOX
 * *---------------------------- */
.ann-wrap {
  width: calc(100% - 0.5rem);
  margin: 0.5rem 0.5rem 0.5rem 0;
  padding: 0.5rem 1rem;
  border: 1px solid #eee;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *お知らせ
 * *********************************************************************** */
#news .container .news-list {
  width: 1080px;
  max-width: 100%;
  margin: 1rem auto 3rem;
  border-top: 1px solid #e7e7e7;
}
@media screen and (max-width: 767px) {
  #news .container .news-list {
    margin: 0;
  }
}
#news .container .news-list li {
  position: relative;
  display: block;
  opacity: 1;
  height: auto;
}
#news .container .news-list li.is-hidden {
  display: none;
  opacity: 0;
  height: 0;
  margin: 0;
}
#news .container .news-list li:after {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  border-top: 3px solid #e60012;
  border-left: 3px solid #e60012;
  transform: rotate(135deg);
  position: absolute;
  right: 7px;
  top: calc(50% - 5px);
}
@media screen and (max-width: 767px) {
  #news .container .news-list li:after {
    border-top: 2px solid #e60012;
    border-left: 2px solid #e60012;
  }
}
#news .container .news-list li dl {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  #news .container .news-list li dl {
    flex-direction: column;
    width: 100%;
  }
}
#news .container .news-list li dl dt, #news .container .news-list li dl dd {
  padding: 1.8rem 0.5rem 1.9rem;
  line-height: 1.6;
  border-bottom: 1px solid #e7e7e7;
}
@media screen and (max-width: 767px) {
  #news .container .news-list li dl dt, #news .container .news-list li dl dd {
    line-height: 1.6;
    font-size: 3.5vw;
  }
}
#news .container .news-list li dl dt {
  width: 8rem;
}
@media screen and (max-width: 767px) {
  #news .container .news-list li dl dt {
    width: 100%;
    margin: 0;
    padding: 1.3rem 0.5rem 0.2rem;
    border-bottom: none;
  }
}
#news .container .news-list li dl dd {
  width: calc(100% - 8rem);
  padding: 1.8rem 2.8rem 1.9rem 0.5rem;
}
@media screen and (max-width: 767px) {
  #news .container .news-list li dl dd {
    width: 100%;
    margin: 0;
    padding: 0 2rem 1.6rem 0.5rem;
  }
}
#news .container .news-list li dl dd a {
  color: #444;
}
#news .container .news-list li dl dd a:hover {
  color: #e60012;
}
#news .container .news-detail-contents {
  width: 1080px;
  max-width: 100%;
  margin: 0 auto;
}
#news .container .news-detail-contents h2 {
  margin: 1.5rem 0 1.5rem;
  line-height: 1.6;
}
@media screen and (max-width: 767px) {
  #news .container .news-detail-contents h2 {
    margin: 0 0 0.5rem;
    text-align: left;
    line-height: 1.5;
  }
}
#news .container .news-detail-contents h2 span {
  display: inline-block;
  width: auto;
  margin: 0 1rem 0 0;
  color: #e60012;
  font-weight: 700;
}
#news .container .news-detail-contents p {
  margin: 0.8rem 0;
  line-height: 1.8;
}
#news .container .news-detail-contents p.date {
  margin: 0 0 3rem;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #news .container .news-detail-contents p.date {
    margin: 0 0 1rem;
  }
}
#news .container .news-detail-contents p.back-list {
  margin: 3rem 0 0;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #news .container .news-detail-contents p.back-list {
    margin: 1rem 0 0;
  }
}
#news .container .news-detail-contents p.back-list a {
  position: relative;
  display: inline-block;
  padding: 0 1.7rem 0 0;
  color: #0084c1;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #news .container .news-detail-contents p.back-list a {
    padding: 0 1.4rem 0 0;
  }
}
#news .container .news-detail-contents p.back-list a:hover {
  text-decoration: underline;
}
#news .container .news-detail-contents p.back-list a:after {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  border-top: 3px solid #e60012;
  border-left: 3px solid #e60012;
  transform: rotate(135deg);
  position: absolute;
  right: 7px;
  top: calc(50% - 4px);
}
@media screen and (max-width: 767px) {
  #news .container .news-detail-contents p.back-list a:after {
    border-top: 2px solid #e60012;
    border-left: 2px solid #e60012;
    top: calc(50% - 3px);
  }
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *会社案内
 * *********************************************************************** */
#static table.tbl-otherItem th, #static table.tbl-otherItem td {
  padding: 35px 40px;
  line-height: 1.6;
}
@media screen and (max-width: 767px) {
  #static table.tbl-otherItem th, #static table.tbl-otherItem td {
    padding: 1rem;
  }
}
#static table.tbl-otherItem th {
  width: 300px;
}
@media screen and (max-width: 767px) {
  #static table.tbl-otherItem th {
    width: 100%;
  }
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *QA
 * *********************************************************************** */
#qa {
  padding: 40px 3rem 100px;
}
@media screen and (max-width: 767px) {
  #qa {
    padding: 4vw 4vw 6vw;
  }
}
#qa .qa-menu {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 1080px;
  max-width: 100%;
  margin: 0 auto 64px;
}
@media screen and (max-width: 767px) {
  #qa .qa-menu {
    flex-direction: column;
    margin: 0 0 6vw;
    padding: 2vw 0 0;
  }
}
#qa .qa-menu .menu-container {
  width: 50%;
  margin: 0;
  padding: 1.5rem;
}
@media screen and (max-width: 767px) {
  #qa .qa-menu .menu-container {
    width: 100%;
    padding: 0 0 4vw;
  }
}
#qa .qa-menu .menu-container h2.qa-ttl {
  position: relative;
  margin: 0 0 0.4rem;
  padding: 0;
  font-size: 1.4rem;
  font-weight: 500;
  color: #e60012;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #qa .qa-menu .menu-container h2.qa-ttl {
    margin: 0 0 1.7vw;
    padding: 0 1vw 0 0;
    font-size: 4.8vw;
    font-weight: 600;
  }
}
#qa .qa-menu .menu-container h2.qa-ttl a {
  font-size: 1.4rem;
  font-weight: 500;
}
#qa .qa-menu .menu-container h2.qa-ttl a:hover {
  color: #e60012;
}
#qa .qa-menu .menu-container h2.qa-ttl a img {
  width: 36px;
  height: 36px;
  margin: 0 0.5rem 0 0;
}
#qa .qa-menu .menu-container ul {
  padding: 0;
}
#qa .qa-menu .menu-container ul li {
  padding: 0.2rem 0 0.2rem 2.5rem;
  text-indent: -0.7rem;
}
@media screen and (max-width: 767px) {
  #qa .qa-menu .menu-container ul li {
    padding: 0 0 0.3rem 2.3rem;
    text-indent: -0.68rem;
  }
}
#qa .qa-menu .menu-container ul li:before {
  display: inline-block;
  content: "⚫︎";
  margin: 0 0.5rem 0 0;
  color: #e60012;
  font-size: 0.7rem;
  line-height: 1.8;
}
#qa .qa-menu .menu-container ul li a {
  font-size: 16px;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #qa .qa-menu .menu-container ul li a {
    font-size: 3.5vw;
    line-height: 1.4;
  }
}
#qa .qa-menu .menu-container ul li a:hover {
  color: #e60012;
}
#qa .qa-wrapper h3.qa-ttl {
  margin: 0;
  padding: 1.4rem 1rem;
  font-size: 1.5rem;
  font-weight: 500;
  background: linear-gradient(90deg, rgb(202, 191, 191), rgb(243, 239, 239));
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper h3.qa-ttl {
    padding: 4vw 4vw;
    font-size: 4.8vw;
    text-align: left;
  }
}
#qa .qa-wrapper ul {
  margin: 0 0 80px;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper ul {
    margin: 0 0 10vw;
  }
}
#qa .qa-wrapper ul li {
  padding: 2.5rem 3rem 2.7rem;
  border-bottom: 1px solid #ddd;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper ul li {
    padding: 1.5rem 1rem 1.7rem;
  }
}
#qa .qa-wrapper ul li dl {
  font-size: 1.2rem;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper ul li dl {
    font-size: 1rem;
  }
}
#qa .qa-wrapper ul li dl dt {
  position: relative;
  margin: 0 0 1rem;
  padding: 0 0 0 4.5rem;
  color: #e60012;
  font-size: 22px;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper ul li dl dt {
    margin: 0 0 2vw;
    padding: 0 0 0 3rem;
    font-size: 3.8vw;
    line-height: 1.4;
  }
}
#qa .qa-wrapper ul li dl dt:before {
  position: absolute;
  top: 0;
  left: 0;
  content: "Q. ";
  font-size: 2rem;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper ul li dl dt:before {
    top: 0.3vw;
    font-size: 4.8vw;
  }
}
#qa .qa-wrapper ul li dl dd {
  position: relative;
  padding: 0 0 0 4.5rem;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper ul li dl dd {
    padding: 0 0 0 3rem;
    font-size: 3.5vw;
    line-height: 1.5;
  }
}
#qa .qa-wrapper ul li dl dd:before {
  position: absolute;
  top: 0;
  left: 0.1rem;
  content: "A. ";
  font-size: 2rem;
  font-weight: 600;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper ul li dl dd:before {
    top: 0.3vw;
    font-size: 4.8vw;
  }
}

/* ---------------------------
 * *アコーディオンの場合　-ac
 * *----------------------------- */
#qa .menu-ttl {
  margin: 2.5rem 0.5rem 0.6rem;
  font-size: 1.4rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #qa .menu-ttl {
    margin: 1.5rem 0.2rem 0.5rem;
    font-size: 1.1rem;
  }
}
#qa .qa-wrapper-ac {
  padding: 0;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac {
    padding: 0;
  }
}
#qa .qa-wrapper-ac li {
  padding: 0;
}
#qa .qa-wrapper-ac li:hover {
  opacity: 1;
}
#qa .qa-wrapper-ac li:last-child dl {
  border-bottom: 1px solid #f8f8f8;
}
#qa .qa-wrapper-ac li dl {
  margin: 0 0 0.2rem;
  border-top: 1px solid #f8f8f8;
  border-left: 1px solid #f8f8f8;
  border-right: 1px solid #f8f8f8;
}
#qa .qa-wrapper-ac li dl dt {
  position: relative;
  display: block;
  padding: 1.1rem 4rem;
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1.4;
  background-color: #f8f8f8;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dt {
    padding: 1rem 3.4rem 1rem 3rem;
    font-size: 1rem;
    line-height: 1.6;
  }
}
#qa .qa-wrapper-ac li dl dt:hover {
  cursor: pointer;
  color: #e60012;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dt:hover {
    color: #000;
  }
}
#qa .qa-wrapper-ac li dl dt:before {
  position: absolute;
  top: 0.7rem;
  left: 0.5rem;
  content: "Q.";
  padding: 0.4rem 0.8rem;
  color: #e60012;
  font-size: 1.4rem;
  font-weight: 500;
  line-height: 1;
  box-sizing: border-box;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dt:before {
    top: 0.75rem;
    left: 0.4rem;
    padding: 0.2rem 0.5rem;
  }
}
#qa .qa-wrapper-ac li dl dt .btn {
  position: absolute;
  top: calc(50% - 7px);
  right: 1.5rem;
  width: 16px;
  height: 16px;
  transform-origin: center center;
  transition-duration: 0.2s;
}
#qa .qa-wrapper-ac li dl dt .btn:before, #qa .qa-wrapper-ac li dl dt .btn:after {
  content: "";
  background-color: #e60012;
  border-radius: none;
  width: 16px;
  height: 2px;
  position: absolute;
  top: 7px;
  left: 0;
  transform-origin: center center;
}
#qa .qa-wrapper-ac li dl dt .btn:before {
  width: 2px;
  height: 16px;
  top: 0;
  left: 7px;
}
#qa .qa-wrapper-ac li dl dt.is-active .btn {
  transform: rotate(-180deg);
}
#qa .qa-wrapper-ac li dl dt.is-active .btn:before {
  content: none;
}
#qa .qa-wrapper-ac li dl dt::-webkit-details-marker {
  display: none;
}
#qa .qa-wrapper-ac li dl dd {
  display: none;
  padding: 1.1rem 1rem 1.1rem 4rem;
  background-color: #fdfdfd;
  border-bottom: 1px solid #f8f8f8;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dd {
    padding: 1rem 1rem 1rem 1.1rem;
  }
}
#qa .qa-wrapper-ac li dl dd p {
  position: relative;
  margin: 0;
  padding: 0 3rem 0 2.5rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.7;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dd p {
    padding: 0 0 0 1.9rem;
    font-size: 1rem;
    line-height: 1.6;
  }
}
#qa .qa-wrapper-ac li dl dd p:before {
  position: absolute;
  top: -0.2rem;
  left: 0;
  content: "A.";
  padding: 0.4rem 0;
  color: #e60012;
  font-size: 1.4rem;
  font-weight: 500;
  line-height: 1;
  box-sizing: border-box;
}
@media screen and (max-width: 767px) {
  #qa .qa-wrapper-ac li dl dd p:before {
    top: -0.15rem;
    left: 0;
    padding: 0.2rem 0;
    font-size: 1.3rem;
  }
}
/*# sourceMappingURL=other.css.map */
