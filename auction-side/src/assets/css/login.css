@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *ログインページ
 * *********************************************************************** */
#main #login-form {
  margin: 60px auto 60px;
  padding: 1rem 1rem 60px;
}
@media screen and (max-width: 767px) {
  #main #login-form {
    margin: 30px auto 30px;
    padding: 1rem 1rem 30px;
  }
}
#main #login-form table.tbl-login {
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
  padding: 0 120px;
}
#main #login-form table.tbl-login th {
  font-size: 16px;
  font-weight: 700;
  vertical-align: middle;
  width: 200px;
  position: relative;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th {
    display: block;
    width: 100%;
  }
}
#main #login-form table.tbl-login th em.req {
  font-size: 12px;
  color: #e60012;
  display: inline-block;
  position: relative;
  top: -8px;
  margin-left: 5px;
}
@media screen and (min-width: 768px) {
  #main #login-form table.tbl-login th span.note:hover + .note-modal {
    display: block;
    -webkit-animation-name: fade-basic;
            animation-name: fade-basic;
    -webkit-animation-duration: 0.5s;
            animation-duration: 0.5s;
  }
}
#main #login-form table.tbl-login td {
  padding: 15px 0;
  position: relative;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login td {
    display: block;
    width: 100%;
    padding: 10px 0 20px;
  }
}
#main #login-form table.tbl-login td.check-idpass {
  padding-left: 10px;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login td.check-idpass {
    padding-left: 0;
    text-align: center;
  }
}
#main #login-form table.tbl-login td input {
  width: 360px;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login td input {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login td p.err-txt {
    position: static;
    -webkit-transform: none;
            transform: none;
    max-width: 100%;
    margin-top: 5px;
  }
}
#main #login-form table.tbl-login th span.note {
  display: inline-block;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 1;
  padding-top: 3px;
}
#main #login-form table.tbl-login th span.note:hover {
  cursor: pointer;
}
#main #login-form table.tbl-login th .note-modal {
  display: none;
  z-index: 1;
  position: absolute;
  top: 85px;
  left: -60px;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .note-modal {
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
  }
}
#main #login-form table.tbl-login th .note-modal .note-txt {
  text-align: center;
  background-color: #fff;
  -webkit-box-shadow: 0px 5px 15px 1px rgba(0, 0, 0, 0.08);
          box-shadow: 0px 5px 15px 1px rgba(0, 0, 0, 0.08);
  width: 500px;
  padding: 30px 15px;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .note-modal .note-txt {
    position: fixed;
    width: calc(100vw - 50px);
    top: 50%;
    right: 50%;
    -webkit-transform: translate(50%, -50%);
            transform: translate(50%, -50%);
  }
}
#main #login-form table.tbl-login th .note-modal .note-txt:before {
  content: "";
  display: block;
  border-right: 10px solid transparent;
  border-bottom: 14px solid #fff;
  border-left: 10px solid transparent;
  position: absolute;
  top: -14px;
  left: 150px;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .note-modal .note-txt:before {
    display: none;
  }
}
#main #login-form table.tbl-login th .note-modal .note-txt p {
  display: inline-block;
  font-size: 16px;
  color: #e60012;
  font-weight: 500;
  line-height: 2.2;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .note-modal .note-txt p {
    line-height: 1.8;
  }
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .modal__bg {
    background: rgba(0, 0, 0, 0.7);
    height: 100vh;
    position: absolute;
    width: 100vw;
  }
}
#main #login-form .forget-pass {
  text-align: center;
}
#main #login-form .forget-pass a {
  display: inline-block;
  font-weight: 500;
  color: #6c6c6c;
  font-size: 14px;
  border-bottom: 1px solid #6c6c6c;
  padding: 2px;
}
#main #login-form .rule {
  max-width: 640px;
  margin: 70px auto 0;
}
#main #login-form .rule embed {
  border: 1px solid #ccc;
}
#main #login-form .rule p.tit-rule {
  font-weight: 500;
  color: #000;
  font-size: 16px;
  padding-left: 10px;
}
#main #login-form .rule .rule-check {
  margin-top: 25px;
  text-align: center;
}
#main #login-form .btn-form {
  margin-top: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  #main #login-form .btn-form {
    margin-top: 30px;
  }
}
#main #login-form .id-pass-err p.err-txt {
  width: 100%;
  text-align: center;
  margin-top: 30px;
}
#main #login-form .id-pass-err p.err-txt p.err-txt {
  position: absolute;
  top: 50%;
  left: 370px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 100%;
  max-width: 340px;
}
#main #login-form .rule .rule-check p.err-txt {
  margin-top: 5px;
  text-align: center;
}
#main #login-form .request {
  margin-top: 40px;
  text-align: center;
}
#main #login-form .request a {
  font-weight: 700;
  color: #e60012;
  font-size: 16px;
  border-bottom: 1px solid #e60012;
  padding: 2px;
}
#main #login-form .request p {
  text-align: center;
  font-weight: 700;
  color: #404040;
  font-size: 12px;
  margin-top: 10px;
}
#main.reminder [class^=remind-msg] {
  margin: 60px 0 0;
  padding: 0 1rem;
}
@media screen and (max-width: 767px) {
  #main.reminder [class^=remind-msg] {
    margin: 40px 0 0;
  }
}
#main.reminder [class^=remind-msg] p {
  text-align: center;
  font-weight: 500;
  font-size: 1.1rem;
}
@media screen and (max-width: 767px) {
  #main.reminder [class^=remind-msg] p {
    font-size: 0.9rem;
    text-align: left;
  }
}
#main.reminder [class^=remind-msg] p.remind-txt-att {
  font-size: 1.2rem;
}
#main.reminder [class^=remind-msg] p.remind-txt-att span {
  display: block;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main.reminder [class^=remind-msg] p.remind-txt-att span {
    display: inline-block;
    font-size: 0.9rem;
    text-align: left;
  }
}
#main.reminder .remind-msg-comp {
  margin: 160px 0;
}
@media screen and (max-width: 767px) {
  #main.reminder .remind-msg-comp {
    margin: 80px 0;
  }
}
#main.reminder .remind-msg-comp .remind-comp-btn {
  width: 360px;
  margin: 40px auto 0;
}
@media screen and (max-width: 767px) {
  #main.reminder .remind-msg-comp .remind-comp-btn {
    width: 100%;
  }
}
#main.reminder #login-form {
  margin: 60px 0;
}
@media screen and (max-width: 767px) {
  #main.reminder #login-form {
    margin: 40px 0;
    padding: 15px;
  }
}
#main.reminder #login-form table.tbl-login th {
  width: 190px;
}
/*# sourceMappingURL=login.css.map */