@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *一覧ページ　落札結果　検索欄
 * *********************************************************************** */
#home #main #search-condition .container .panel-search-ope {
  border: none;
  -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}
#home #main #search-condition .container .panel-results {
  background-color: #c8e0eb;
  -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}

#result #main {
  background-image: url(../img/result/bg_main_result.png);
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: top;
}
@media screen and (max-width: 767px) {
  #result #main {
    background-image: url(../img/result/bg_main_result_sp.png);
  }
}
#result #main #search-condition {
  padding: 0 1rem 80px;
}
@media screen and (max-width: 767px) {
  #result #main #search-condition {
    padding: 30px 4vw;
  }
}
#result #main #search-condition .container .panel-search-ope {
  border: 1px solid #ededed;
  -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}
#result #main #search-condition .container .panel-results {
  background-color: #e7dcd8;
  -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}
#result #main #list-auction .container {
  padding: 0 0 80px;
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container {
    padding: 4vw;
  }
}
#result #main #list-auction .container .auction-conteiner .wrap-btn.add-favorite {
  width: 100%;
  margin: 1rem 0;
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container .auction-conteiner .wrap-btn.add-favorite {
    margin: 4vw 0;
  }
}
#result #main #list-auction .container .auction-conteiner .auction-contents {
  margin: 20px auto;
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container .auction-conteiner .auction-contents {
    margin: 20px 0;
  }
}
#result #main #list-auction .container .auction-conteiner .auction-contents .status {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0;
  padding: 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container .auction-conteiner .auction-contents .status {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0;
  }
}
#result #main #list-auction .container .auction-conteiner .auction-contents .status .num-results {
  margin: 0 0 0.5rem;
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container .auction-conteiner .auction-contents .status .num-results {
    width: 100%;
    margin: 2vw 0;
    padding: 0 1vw;
    text-align: left;
    font-size: 3.5vw;
  }
}
#result #main #list-auction .container .auction-conteiner .auction-contents .status .num-results span {
  font-size: 16px;
  display: inline-block;
  margin: 0 0.2rem 0 0;
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container .auction-conteiner .auction-contents .status .num-results span {
    font-size: 3.5vw;
  }
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container .auction-conteiner .auction-contents .status .select-favorite {
    width: 100%;
    font-size: 3.5vw;
    text-align: right;
  }
}
#result #main #list-auction .container .auction-conteiner .auction-contents .status .select-favorite input {
  width: auto;
  margin: 0 0.5rem;
  padding: 0;
  color: #0084c1;
  text-decoration: underline;
  background-color: transparent;
}
#result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table {
  width: 100%;
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table {
    margin: 0 0 4vw;
  }
}
#result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table table {
  width: 100%;
}
#result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table table thead {
  color: #fff;
  border-top-left-radius: 12px;
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table table thead {
    display: none;
  }
}
#result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody {
  width: 100%;
}
#result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr {
  border-bottom: 1px solid #fff;
}
@media screen and (max-width: 767px) {
  #result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr {
    width: 100%;
    padding: 4vw;
    border-bottom: 1px solid #d9d9d9;
  }
}
#result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr:nth-child(odd) {
  background-color: #faf9f8;
}
#result #main #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr:nth-child(even) {
  background-color: #f2f0ee;
}
#result #list-head {
  padding: 60px 1rem 0;
}
@media screen and (max-width: 767px) {
  #result #list-head {
    padding: 1rem;
  }
}
#result #list-head .search-panel-wrap {
  padding: 0;
  background-color: #f0f0f0;
}
#result #list-head .search-panel-wrap .head {
  padding: 0.5rem 1rem;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  background-color: #000;
  text-align: center;
}
#result #list-head .search-panel-wrap .contents {
  padding: 3rem;
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents {
    padding: 1rem 1rem 2rem;
  }
}
#result #list-head .search-panel-wrap .contents .keyword {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0 1rem 2rem;
  border-bottom: 1px solid #999;
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .keyword {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0 0.5rem 1.5rem;
  }
}
#result #list-head .search-panel-wrap .contents .keyword__label {
  width: 180px;
  padding: 11px 0;
}
#result #list-head .search-panel-wrap .contents .keyword input {
  width: calc(100% - 180px);
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .keyword input {
    width: 100%;
  }
}
#result #list-head .search-panel-wrap .contents .model {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 1rem 1rem;
  border-bottom: 1px solid #999;
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .model {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0.5rem 0.5rem 1rem;
  }
}
#result #list-head .search-panel-wrap .contents .model .model__label {
  width: 180px;
  padding: 11px 0;
}
#result #list-head .search-panel-wrap .contents .model .model__contents {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: calc(100% - 180px);
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .model .model__contents {
    width: 100%;
  }
}
#result #list-head .search-panel-wrap .contents .model .model__contents .label-item {
  display: inline-block;
  margin: 0.3rem 0.5rem;
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .model .model__contents .label-item {
    margin: 0.3rem 0.5rem 0.3rem 0;
  }
}
#result #list-head .search-panel-wrap .contents .model .model__contents .label-item input {
  -webkit-transform: scale(1.2);
          transform: scale(1.2);
}
#result #list-head .search-panel-wrap .contents .model .model__contents .label-item label {
  margin: 0 0.5rem;
}
#result #list-head .search-panel-wrap .contents .other {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid #999;
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .other {
    padding: 1.5rem 0.5rem;
  }
}
#result #list-head .search-panel-wrap .contents .other input {
  -webkit-transform: scale(1.6);
          transform: scale(1.6);
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .other input {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
  }
}
#result #list-head .search-panel-wrap .contents .other label {
  display: inline-block;
  margin: 0 1rem;
}
#result #list-head .search-panel-wrap .contents .brand {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 1rem 1rem;
  border-bottom: 1px solid #999;
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .brand {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 0.5rem 0.5rem 1rem;
  }
}
#result #list-head .search-panel-wrap .contents .brand .brand__label {
  width: 180px;
  padding: 11px 0;
}
#result #list-head .search-panel-wrap .contents .brand .brand__contents {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: calc(100% - 180px);
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .brand .brand__contents {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    width: 100%;
  }
}
#result #list-head .search-panel-wrap .contents .brand .brand__contents button.btn {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 280px;
  max-width: 100%;
  height: 46px;
  margin: 0;
  padding: 0;
  color: #e60012;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.1;
  background-color: #fff;
  border: 2px solid #e60012;
  border-radius: 30px;
}
@media screen and (max-width: 767px) {
  #result #list-head .search-panel-wrap .contents .brand .brand__contents button.btn {
    width: 280px;
    max-width: 94%;
  }
}
#result #list-head .search-panel-wrap .contents .brand .brand__contents button.btn::after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #e60012;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
#result #list-head .search-panel-wrap .contents .wrap-btn {
  margin: 0 auto;
  text-align: center;
}
#result #list-head .search-panel-wrap .contents .wrap-btn button.search {
  width: 280px;
  height: 55px;
  margin: 2rem auto 0;
  padding: 0.5rem 1rem;
  color: #fff;
  font-size: 1.1rem;
  background-color: #e60012;
  border-radius: 30px;
}
#result #list-head .conditions {
  margin: 1rem auto 0;
  padding: 1rem;
  background-color: #f0f0f0;
}
#result #list-head .conditions .conditions__label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0 1rem 0.3rem;
  border-bottom: 1px solid #999;
}
@media screen and (max-width: 767px) {
  #result #list-head .conditions .conditions__label {
    padding: 0 0.5rem 0.3rem;
  }
}
#result #list-head .conditions .conditions__label .ttl {
  width: 100px;
  padding: 0.2rem 0;
}
#result #list-head .conditions .conditions__label .elm {
  margin: 0 0.5rem 0.5rem 0;
  padding: 0.2rem 1.5rem;
  background-color: #fff;
  border-radius: 20px;
}
#result #list-head .conditions .results__label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0.9rem 1rem 0.2rem;
}
@media screen and (max-width: 767px) {
  #result #list-head .conditions .results__label {
    padding: 1rem 0.5rem 0.2rem;
  }
}
#result #list-head .conditions .results__label .ttl {
  width: 100px;
  font-weight: 700;
}
#result #list {
  width: calc(1280px + 2rem);
  max-width: 100%;
  margin: 0 auto;
  padding: 2rem 1rem 4rem;
  position: relative;
}
@media screen and (max-width: 767px) {
  #result #list {
    padding: 1.5rem 1rem 2rem;
  }
}
#result #list .container .display-option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: flex-between;
      -ms-flex-pack: flex-between;
          justify-content: flex-between;
  margin: 0 0 1rem;
  border-bottom: 1px solid #999;
}
@media screen and (max-width: 767px) {
  #result #list .container .display-option {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#result #list .container .display-option .sorting {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: 50%;
  padding: 1rem 0;
}
@media screen and (max-width: 767px) {
  #result #list .container .display-option .sorting {
    width: 100%;
  }
}
#result #list .container .display-option .sorting button.menu_trigger {
  position: relative;
  width: auto;
  min-width: 125px;
  padding: 0 1.5rem 0 0.5rem;
  font-size: 1rem;
  text-align: left;
  background-color: #fff;
}
#result #list .container .display-option .sorting button.menu_trigger:after {
  position: absolute;
  top: calc(50% - 8px);
  right: 1px;
  color: #000;
  font-size: 1rem;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 1;
  -webkit-transform: rotateZ(90deg);
          transform: rotateZ(90deg);
}
#result #list .container .display-option .sorting ul.sorting_panel {
  display: none;
  position: absolute;
  top: 55px;
  left: 0;
  width: 200px;
  max-width: calc(100% - 2rem);
  margin: 0;
  padding: 0.8rem 0 1rem;
  z-index: 10;
  background-color: #fff;
  border: 1ps solid #eee;
  -webkit-box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35);
          box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35);
}
#result #list .container .display-option .sorting ul.sorting_panel.is-active {
  display: block;
}
#result #list .container .display-option .sorting ul.sorting_panel .option_item {
  margin: 0;
  padding: 0.4rem 1.2rem;
}
#result #list .container .display-option .sorting ul.sorting_panel .option_item:hover {
  background-color: #eee;
}
#result #list .container .display-option .sorting ul.sorting_panel .option_item a {
  cursor: pointer;
}
#result #list .container .display-option .switch {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  width: 50%;
  padding: 0.8rem 0;
}
@media screen and (max-width: 767px) {
  #result #list .container .display-option .switch {
    width: 100%;
    border-top: 1px solid #999;
  }
}
#result #list .container .display-option .switch .number-switch {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0.2rem 1rem;
}
@media screen and (max-width: 767px) {
  #result #list .container .display-option .switch .number-switch {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
    width: 100%;
    padding: 0.2rem 0.5rem;
  }
}
#result #list .container .display-option .switch .number-switch .label {
  margin: 0 1rem 0 0;
}
#result #list .container .display-option .switch .number-switch .num .btn {
  display: inline-block;
  margin: 0 0.5rem;
  padding: 0;
  color: #e5e5e5;
  font-size: 1rem;
  background-color: transparent;
}
#result #list .container .display-option .switch .number-switch .num .btn.is-active {
  color: #000;
  text-decoration: underline;
}
#result #list .container .display-option .switch .number-switch .num .btn:hover {
  color: #000;
  text-decoration: underline;
}
#result #list .container .display-option .switch .display-switch {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0.2rem 1rem;
  border-left: 1px solid #999;
}
@media screen and (max-width: 767px) {
  #result #list .container .display-option .switch .display-switch {
    padding: 0.2rem 0 0.2rem 1rem;
  }
}
#result #list .container .display-option .switch .display-switch p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 0.6rem;
}
@media screen and (max-width: 767px) {
  #result #list .container .display-option .switch .display-switch p {
    margin: 0 0.6rem;
  }
}
#result #list .container .display-option .switch .display-switch p .btn {
  width: 21px;
  height: 21px;
  padding: 0;
  background-color: transparent;
}
#result #list .container .display-option .switch .display-switch p .btn.row {
  background: url("../img/common/icn_list_switch_row_off.svg") center top no-repeat;
  background-size: 21px 21px;
  background-position: calc(50% - 0px) calc(50% - 0px);
  background-clip: content-box;
}
#result #list .container .display-option .switch .display-switch p .btn.row.is-active, #result #list .container .display-option .switch .display-switch p .btn.row:hover {
  background: url("../img/common/icn_list_switch_row_on.svg") center top no-repeat;
}
#result #list .container .display-option .switch .display-switch p .btn.panel {
  background: url("../img/common/icn_list_switch_panel_off.svg") center top no-repeat;
  background-size: 21px 21px;
  background-position: calc(50% - 0px) calc(50% - 0px);
  background-clip: content-box;
}
#result #list .container .display-option .switch .display-switch p .btn.panel.is-active, #result #list .container .display-option .switch .display-switch p .btn.panel:hover {
  background: url("../img/common/icn_list_switch_panel_on.svg") center top no-repeat;
}
#result #list .container .wrap-btn {
  width: 100%;
  margin: 3rem 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #result #list .container .wrap-btn {
    margin: 2rem 0 0;
  }
}
#result #list .container .wrap-btn .list-more {
  width: 300px;
  height: 55px;
  margin: 0 auto;
  padding: 0.5rem 2rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  background-color: #bf2a24;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #result #list .container .wrap-btn .list-more {
    height: 60px;
  }
}
#result #list .container .item-list.row {
  margin: 0;
  text-align: center;
}
#result #list .container .item-list.row.no-item {
  padding: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #fff;
}
#result #list .container .item-list.row.no-item p.no-item-msg {
  text-align: center;
  font-size: 22px;
  font-weight: 700;
  color: #000;
}
#result #list .container .item-list.row ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
}
#result #list .container .item-list.row ul > li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  margin: 0;
  border: 1px solid #e5e5e5;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li {
    margin: 0 0 1.5 rem;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#result #list .container .item-list.row ul > li + li {
  margin: 2rem 0 0;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li + li {
    margin: 1.5rem 0 0;
  }
}
#result #list .container .item-list.row ul > li a {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  margin: 0;
}
#result #list .container .item-list.row ul > li a figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 150px;
  height: 150px;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a figure {
    width: 100px;
    height: auto;
  }
}
#result #list .container .item-list.row ul > li a figure img {
  max-width: 150px;
  height: auto;
  max-height: 150px;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a figure img {
    width: 100px;
    max-width: 100px;
    max-height: 100px;
    height: auto;
  }
}
#result #list .container .item-list.row ul > li a figure .status {
  position: absolute;
  bottom: 10px;
  left: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  height: auto;
}
#result #list .container .item-list.row ul > li a figure .status > p {
  display: inline-block;
  width: auto;
  max-width: 110px;
  margin: 2px 0;
  padding: 2px 12px;
  font-size: 0.8rem;
  font-weight: 700;
  text-align: center;
  border-radius: 20px;
}
#result #list .container .item-list.row ul > li a figure .status .status_top {
  color: #fff;
  background-color: #ff0000;
}
#result #list .container .item-list.row ul > li a figure .status .status_overbid {
  color: #ff0000;
  background-color: #D3D1D0;
}
#result #list .container .item-list.row ul > li a figure .status_soldout {
  position: absolute;
  top: 26%;
  left: 10%;
  width: 80%;
}
#result #list .container .item-list.row ul > li a figure .status_soldout img {
  width: 100%;
}
#result #list .container .item-list.row ul > li a .favorite {
  position: absolute;
  top: auto;
  bottom: 5px;
  right: 10px;
  left: auto;
  width: 34px;
  height: 34px;
  padding: 2px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
  background-image: url(../img/common/icn_favorite.svg);
  background-size: 26px auto;
  background-position: 3px 3px;
  background-repeat: no-repeat;
  z-index: 1;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .favorite {
    top: 5px;
    left: 5px;
    bottom: auto;
    right: auto;
    width: 28px;
    height: 28px;
    background-size: 20px auto;
    background-position: 3px 3px;
    background-repeat: no-repeat;
  }
}
#result #list .container .item-list.row ul > li a .favorite::hover {
  opacity: 0.7;
}
#result #list .container .item-list.row ul > li a .favorite.active {
  background-image: url(../img/common/icn_favorite_on.svg);
  background-size: 26px auto;
  background-position: 3px 3px;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .favorite.active {
    top: 5px;
    left: 5px;
    bottom: auto;
    right: auto;
    width: 28px;
    height: 28px;
    background-size: 20px auto;
    background-position: 3px 3px;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap {
  position: relative;
  padding: 0 0 44px;
  width: calc(100% - 150px);
  min-height: 150px;
  border-left: 1px solid #e5e5e5;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap {
    width: calc(100% - 100px);
    padding: 0;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap .item-name {
  position: relative;
  width: 100%;
  margin: 0;
  padding: 0.5rem 110px 0.5rem 1rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.3;
  background-color: #444;
  border: none;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap .item-name {
    height: auto !important;
    padding: 0.5rem 1rem 0.5rem 1rem;
    font-size: 0.8rem;
    line-height: 1.1rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap .item-name .tag_status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  margin: 0;
  padding: 0;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap .item-name .tag_status {
    position: static;
    display: block;
    width: 100%;
    padding: 0.5rem 0 0.3rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap .item-name .tag_status > p {
  display: inline-block;
  margin: 0 2px 2px 0;
  padding: 4px 12px 5px;
  font-size: 0.8rem;
  font-weight: 700;
  line-height: 1;
  border-radius: 20px;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap .item-name .tag_status > p {
    padding: 5px 16px 6px;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap .item-name .tag_status .status_recommend {
  color: #fff;
  background-color: #ff0000;
}
#result #list .container .item-list.row ul > li a .product-wrap .current-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  width: 100%;
  padding: 0.5rem 1rem;
  background-color: #fff;
}
#result #list .container .item-list.row ul > li a .product-wrap .current-price .price-v {
  display: inline-block;
  margin: 0 0.5rem;
  color: #E50A09;
  font-size: 1.4rem;
  font-weight: 700;
  line-height: 1.2;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap .current-price .price-v {
    font-size: 1.4rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: 100%;
  height: 44px;
  margin: 0 auto;
  padding: 0.5rem 1rem;
  background-color: #f0f0f0;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl {
    position: static;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    width: 100%;
    height: auto;
    border-bottom: 1px solid #fff;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dt {
  width: 180px;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dt {
    width: 100%;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dt .bid-l {
  font-weight: 400;
  display: inline-block;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dt .bid-l {
    width: 4rem;
    font-size: 0.8rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dt .bid-v {
  margin: 0 0 0 1rem;
  font-weight: 600;
  display: inline-block;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dt .bid-v {
    margin: 0 0 0 0.5rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dd {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: calc(100% - 180px);
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dd {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    width: 100%;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dd .end-l {
  width: 140px;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dd .end-l {
    padding: 0;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dd .end-l span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dd .end-l span.label {
    width: 4rem;
    font-size: 0.8rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dd .end-l span.value {
  display: inline-block;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dd .end-l span.value {
    font-size: 0.9rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dd .end-l span.limit {
  color: #ff0000;
}
#result #list .container .item-list.row ul > li a .product-wrap dl dd .end-v {
  width: calc(100% - 140px);
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dd .end-v {
    width: 100%;
    padding: 0;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dd .end-v span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dd .end-v span.label {
    width: 4rem;
    font-size: 0.8rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap dl dd .end-v span.value {
  display: inline-block;
  font-size: 1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap dl dd .end-v span.value {
    font-size: 0.9rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid {
  -ms-grid-row: 1;
  -ms-grid-row-span: 3;
  grid-row: 1/4;
  -ms-grid-column: 2;
  -ms-grid-column-span: 1;
  grid-column: 2/3;
  position: relative;
  width: 360px;
  padding: 1rem 1rem calc(46px + 2rem);
  background-color: #f0f0f0;
}
@media screen and (max-width: 1080px) {
  #result #list .container .item-list.row ul > li a .product-wrap .place-bid {
    width: 240px;
    max-width: 100%;
  }
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap .place-bid {
    -ms-grid-row: 3;
    -ms-grid-row-span: 1;
    grid-row: 3/4;
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
    width: 100%;
    max-width: 100%;
    border-bottom: 1px solid #fff;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .ttl {
  margin: 0 0 0.5rem;
  font-size: 1.2rem;
  font-weight: 700;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .price {
  font-size: 1.4rem;
  font-weight: 700;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input {
  width: 7rem;
  margin: 0 0 0 1rem;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: 700;
  text-align: right;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input::-webkit-input-placeholder {
  color: #ddd;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input::-moz-placeholder {
  color: #ddd;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input:-ms-input-placeholder {
  color: #ddd;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input::-ms-input-placeholder {
  color: #ddd;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input::placeholder {
  color: #ddd;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input.price-bid-comp {
  background-color: #e5e5e5;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input.price-bid-comp::-webkit-input-placeholder {
  color: #000;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input.price-bid-comp::-moz-placeholder {
  color: #000;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input.price-bid-comp:-ms-input-placeholder {
  color: #000;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input.price-bid-comp::-ms-input-placeholder {
  color: #000;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid input.price-bid-comp::placeholder {
  color: #000;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 1rem 0 1rem;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid ul > li > button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 5px 5px 0;
  padding: 0 7px 0 0;
  font-size: 1rem;
  background-color: #fff;
  border: 1px solid #CDCBCA;
  border-radius: 30px;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap .place-bid ul > li > button {
    font-size: 1rem;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid ul > li > button span {
  display: inline-block;
  position: relative;
  width: 20px;
  height: 20px;
  margin: 2px 5px 2px 2px;
  padding: 0 7px;
  color: #fff;
  line-height: 1;
  background-color: #e60012;
  border-radius: 20px;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid ul > li > button span::after {
  content: "+";
  position: absolute;
  top: 0.7px;
  left: 5.5px;
  width: 14px;
  height: 14px;
  color: #fff;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid.invoice {
  margin: 3rem 0 0;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid.invoice {
    margin: 1rem 0 0;
  }
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid button {
  width: calc(100% - 50px - 1rem);
  height: 55px;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #e60012;
  border-radius: 4px;
  line-height: 1.2;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid button.invoice {
  width: 100%;
  padding: 1px 25% 3px 10px;
  text-align: right;
  background-image: url(../img/common/icn_download_list.svg);
  background-repeat: no-repeat;
  background-position: right 10% top 50%;
}
@media screen and (max-width: 1080px) {
  #result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid button.invoice {
    text-align: center;
  }
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid button.invoice {
    padding: 1px 10px 3px 10px;
    text-align: center;
    background-position: right calc(50% - 7.2rem) top 50%;
  }
}
@media screen and (max-width: 400px) {
  #result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid button.invoice {
    padding: 1px 46px 3px 10px;
    background-position: right 6% top 50%;
  }
}
@media screen and (max-width: 1080px) {
  #result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid button {
    width: auto;
    min-width: calc(100% - 55px - 1rem);
  }
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid button {
    width: calc(100% - 55px - 0.5rem);
  }
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid .update {
  position: relative;
  width: 55px;
  height: 55px;
  margin: 0 0 0 1rem;
  padding: 1.5rem 0 0;
  color: #e60012;
  text-align: center;
  background-color: #fff;
  border: 1px solid #CDCBCA;
  border-radius: 30px;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid .update span {
  font-size: 0.8rem;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .button-bid .update::after {
  content: "";
  display: inline-block;
  background: url("../img/common/icn_update_list.svg") center 8px no-repeat;
  background-size: 20px auto;
  width: 30px;
  height: 30px;
  position: absolute;
  top: 0;
  left: calc(50% - 15px);
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .other-info-detail {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  width: calc(100% - 2rem);
  z-index: 10;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .other-info-detail button {
  position: relative;
  width: 100%;
  height: 46px;
  margin: 0;
  padding: 0.5rem 1rem;
  color: #e60012;
  font-size: 1rem;
  font-weight: 500;
  background-color: #fff;
  border: 2px solid #e60012;
  border-radius: 30px;
}
#result #list .container .item-list.row ul > li a .product-wrap .place-bid .other-info-detail button::after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #e60012;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
#result #list .container .item-list.row ul > li.soldout a figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 150px;
  height: 150px;
  padding: 0;
  background-image: url(../img/common/icn_soldout.png);
  background-size: 140px auto;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.row ul > li.soldout a figure:after {
    top: calc(50% - 50px);
    width: 100px;
    height: 100px;
    background-size: 96px auto;
  }
}
#result #list .container .item-list.panel {
  margin: 0;
  text-align: center;
}
#result #list .container .item-list.panel.no-item {
  padding: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #fff;
}
#result #list .container .item-list.panel.no-item p.no-item-msg {
  text-align: center;
  font-size: 22px;
  font-weight: 700;
  color: #000;
}
#result #list .container .item-list.panel ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  padding: 0 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul {
    padding: 0;
  }
}
#result #list .container .item-list.panel ul li {
  width: calc((100% - 60px) / 4);
  margin: 0 15px 15px 0;
  background-color: #fff;
  border: 1px solid #e5e5e5;
}
@media screen and (max-width: 1080px) {
  #result #list .container .item-list.panel ul li {
    width: calc((100% - 45px) / 3);
  }
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li {
    width: calc((100% - 15px) / 2);
  }
}
#result #list .container .item-list.panel ul li:nth-child(4n) {
  margin: 0 15px 15px 0;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li:nth-child(4n) {
    margin: 0 15px 15px 0;
  }
}
#result #list .container .item-list.panel ul li:nth-child(2n) {
  margin: 0 15px 15px 0;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li:nth-child(2n) {
    margin: 0 0 15px 0;
  }
}
#result #list .container .item-list.panel ul li a {
  position: relative;
  display: block;
  height: 100%;
}
#result #list .container .item-list.panel ul li a figure {
  aspect-ratio: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
#result #list .container .item-list.panel ul li a figure img {
  max-height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
#result #list .container .item-list.panel ul li a .favorite {
  position: absolute;
  top: 5px;
  left: 5px;
  width: 34px;
  height: 34px;
  padding: 2px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
  background-image: url(../img/common/icn_favorite.svg);
  background-size: 26px auto;
  background-position: 3px 2px;
  background-repeat: no-repeat;
}
#result #list .container .item-list.panel ul li a .favorite::hover {
  opacity: 0.7;
}
#result #list .container .item-list.panel ul li a .favorite.active {
  background-image: url(../img/common/icn_favorite_on.svg);
  background-size: 26px auto;
  background-position: 3px 2px;
  background-repeat: no-repeat;
}
#result #list .container .item-list.panel ul li a .item-name {
  width: auto;
  height: auto;
  margin: 0 auto;
  padding: 0.5rem 0.5rem 1rem;
  color: #fff;
  background-color: #444;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.4;
  border: none;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li a .item-name {
    font-size: 0.8rem;
  }
}
#result #list .container .item-list.panel ul li a .current-price {
  padding: 0.5rem;
}
#result #list .container .item-list.panel ul li a .current-price .price-c {
  color: #231914;
  font-size: 12px;
}
#result #list .container .item-list.panel ul li a .current-price .price-v {
  color: #E50A09;
  font-size: 1rem;
  font-weight: 700;
}
#result #list .container .item-list.panel ul li a .current-price .price-u {
  color: #E50A09;
  font-size: 1rem;
  font-weight: 700;
}
#result #list .container .item-list.panel ul li a figure img {
  max-width: 100%;
}
#result #list .container .item-list.panel ul li a dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  padding: 0;
}
#result #list .container .item-list.panel ul li a dl dt, #result #list .container .item-list.panel ul li a dl dd {
  background-color: #E8E7E7;
}
#result #list .container .item-list.panel ul li a dl dt {
  width: 90px;
  margin: 0 1px 0 0;
  padding: 0.5rem 0.5rem 0.5rem 2.5rem;
  font-size: 0.8rem;
  background: url("../img/common/icn_hammer_list.png") no-repeat;
  background-size: 22px 24px;
  background-color: #e7e7e7;
  background-position: 10px 50%;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li a dl dt {
    width: 60px;
    padding: 3px;
    font-size: 0.7rem;
    background-size: 18px 20px;
    background-position: 7px 10px;
  }
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li a dl dt .bid-l {
    padding: 8px 0 0 26px;
  }
}
#result #list .container .item-list.panel ul li a dl dt .bid-v {
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li a dl dt .bid-v {
    width: 100%;
    padding: 5px 0 0;
    font-size: 0.9rem;
    text-align: center;
  }
}
#result #list .container .item-list.panel ul li a dl dd {
  width: calc(100% - 91px);
  margin: 0;
  padding: 0.5rem 0.5rem 0.5rem 2.7rem;
  font-size: 0.8rem;
  background: url("../img/common/icn_clock_list.png") no-repeat;
  background-size: 24px 24px;
  background-color: #e7e7e7;
  background-position: 10px 50%;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li a dl dd {
    width: calc(100% - 61px);
    padding: 3px;
    font-size: 0.7rem;
    background-image: none;
  }
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li a dl dd .end-l {
    padding: 3px;
  }
}
#result #list .container .item-list.panel ul li a dl dd .end-l span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
#result #list .container .item-list.panel ul li a dl dd .end-l span.value {
  display: inline-block;
  font-size: 0.8rem;
  font-weight: 700;
}
#result #list .container .item-list.panel ul li a dl dd .end-l span.limit {
  color: #ff0000;
}
@media screen and (max-width: 767px) {
  #result #list .container .item-list.panel ul li a dl dd .end-v {
    width: 100%;
    padding: 3px;
  }
}
#result #list .container .item-list.panel ul li a dl dd .end-v span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
#result #list .container .item-list.panel ul li a dl dd .end-v span.value {
  display: inline-block;
  font-size: 0.8rem;
  font-weight: 700;
}
#result #list .container .item-list.panel ul li.is-hidden {
  opacity: 0;
  height: 0;
  margin: 0;
}
#result #list .container .item-list.panel ul li.soldout a figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/icn_soldout.png);
  background-size: 92% auto;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;
}
#result #list .container .wrap-btn {
  width: 100%;
  margin: 3rem 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #result #list .container .wrap-btn {
    margin: 1rem 0;
  }
}

/*---------------------------*/
/*モーダル（ブランド選択）*/
/*-----------------------------*/
.modal-brand-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 40px 20px;
  overflow: auto;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  z-index: 100;
}
.modal-brand-container:before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
.modal-brand-container.active {
  opacity: 1;
  visibility: visible;
}
.modal-brand-container .modal-body {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  max-width: calc(100% - 2rem);
  width: 1000px;
  height: 600px;
  margin: 0 auto;
}
.modal-brand-container .modal-body .modal-close {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  top: -26px;
  right: -26px;
  width: 60px;
  height: 60px;
  font-size: 22px;
  color: #fff;
  line-height: 1;
  background-color: #929392;
  border-radius: 40px;
  cursor: pointer;
  z-index: 120;
}
@media screen and (max-width: 767px) {
  .modal-brand-container .modal-body .modal-close {
    top: -28px;
    right: -20px;
  }
}
.modal-brand-container .modal-body .modal-content {
  position: relative;
  padding: 3rem 3rem 2.5rem;
  background-color: #f0f0f0;
  z-index: 110;
}
@media screen and (max-width: 767px) {
  .modal-brand-container .modal-body .modal-content {
    padding: 2.5rem 0.5rem 1.5rem;
  }
}
.modal-brand-container .modal-body .modal-content .category-list {
  height: 400px;
  background-color: #fff;
  overflow-y: auto;
}
.modal-brand-container .modal-body .modal-content .category-list::-webkit-scrollbar {
  background: #fff;
  width: 3px;
  border-radius: 2px;
}
.modal-brand-container .modal-body .modal-content .category-list::-webkit-scrollbar-thumb {
  background-color: #e60012;
  border-radius: 2px;
}
.modal-brand-container .modal-body .modal-content .category-list p.ttl {
  width: 100%;
  padding: 2rem 0.5rem 1.1rem 0.5rem;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  .modal-brand-container .modal-body .modal-content .category-list p.ttl {
    font-size: 0.9rem;
  }
}
.modal-brand-container .modal-body .modal-content .category-list ul.brand-panel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
}
.modal-brand-container .modal-body .modal-content .category-list ul.brand-panel > li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}
.modal-brand-container .modal-body .modal-content .category-list ul.brand-panel > li:first-child {
  border-top: 1px solid #f0f0f0;
}
@media screen and (max-width: 767px) {
  .modal-brand-container .modal-body .modal-content .category-list ul.brand-panel > li {
    width: 100%;
    margin: 0 0 1.5 rem;
    padding: 0;
  }
}
.modal-brand-container .modal-body .modal-content .category-list ul.brand-panel > li .category-l {
  width: 30%;
  padding: 0.8rem 0.2rem 0.8rem 0.5rem;
  font-size: 1rem;
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  .modal-brand-container .modal-body .modal-content .category-list ul.brand-panel > li .category-l {
    width: 40%;
    font-size: 0.9rem;
  }
}
.modal-brand-container .modal-body .modal-content .category-list ul.brand-panel > li .category-s {
  width: 70%;
  padding: 0.3rem;
}
@media screen and (max-width: 767px) {
  .modal-brand-container .modal-body .modal-content .category-list ul.brand-panel > li .category-s {
    width: 60%;
  }
}
.modal-brand-container .modal-body .modal-content .category-list ul.brand-panel > li .category-s ul.brand-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding: 0.5rem 0 0;
}
.modal-brand-container .modal-body .modal-content .category-list ul.brand-panel > li .category-s ul.brand-list li {
  display: inline-block;
  margin: 0 1rem 0.6rem 0;
}
.modal-brand-container .modal-body .modal-content .note-bid {
  width: 100%;
  margin: 1rem 0 0;
  padding: 0.5rem;
  text-align: center;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  .modal-brand-container .modal-body .modal-content .note-bid {
    margin: 0.5rem 0;
    padding: 0.5rem 0.5rem;
  }
}
.modal-brand-container .modal-body .modal-content .button-bid {
  width: 100%;
  padding: 0;
  text-align: center;
}
.modal-brand-container .modal-body .modal-content .button-bid button.search-conditions {
  position: relative;
  width: 280px;
  max-width: 94%;
  height: 46px;
  margin: 0 auto;
  color: #e60012;
  font-size: 1rem;
  font-weight: 500;
  background: #fff;
  border: 2px solid #e60012;
  border-radius: 30px;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  .modal-brand-container .modal-body .modal-content .button-bid button.search-conditions {
    font-size: 0.9rem;
  }
}
.modal-brand-container .modal-body .modal-content .button-bid button.search-conditions.btn::after {
  position: absolute;
  top: calc(50% - 4px);
  right: 15px;
  display: block;
  width: 10px;
  height: 10px;
  padding: 0;
  color: #e60012;
  font-size: 16px;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 0.6;
}
/* 一括登録チェック付き
 * *========================================== */
#search-condition {
  padding: 80px 1rem;
}
@media screen and (max-width: 767px) {
  #search-condition {
    padding: 30px 4vw;
  }
}
#search-condition .container {
  display: -ms-grid;
  display: grid;
  row-gap: 20px;
}
#search-condition .container .panel-search-ope {
  display: -ms-grid;
  display: grid;
  row-gap: 30px;
  padding: 2rem 3rem 2.2rem;
  background-color: #fff;
  border: 1px solid #ededed;
  border-radius: 12px;
  -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-search-ope {
    row-gap: 6vw;
    padding: 4.5vw 8vw 8vw;
  }
}
#search-condition .container .panel-search-ope .cont {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  width: 100%;
  max-width: 1080px;
  margin: 0 auto;
  -webkit-column-gap: 60px;
     -moz-column-gap: 60px;
          column-gap: 60px;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-search-ope .cont {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#search-condition .container .panel-search-ope .cont .condition {
  display: -ms-grid;
  display: grid;
  row-gap: 20px;
  width: calc(100% - 240px);
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-search-ope .cont .condition {
    width: 100%;
    margin: 0 0 8vw;
  }
}
#search-condition .container .panel-search-ope .cont .condition [class$=__label] {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 140px;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-search-ope .cont .condition [class$=__label] {
    margin: 0 0 2vw;
  }
}
#search-condition .container .panel-search-ope .cont .condition [class$=__contents] {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: calc(100% - 140px);
  height: 48px;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-search-ope .cont .condition [class$=__contents] {
    width: 100%;
  }
}
#search-condition .container .panel-search-ope .cont .condition [class$=__contents] .label-item {
  display: inline-block;
  margin: 0.3rem 0.5rem;
}
#search-condition .container .panel-search-ope .cont .condition .keyword, #search-condition .container .panel-search-ope .cont .condition .model {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-search-ope .cont .condition .keyword, #search-condition .container .panel-search-ope .cont .condition .model {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#search-condition .container .panel-search-ope .cont .btn-wrap {
  width: 240px;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-search-ope .cont .btn-wrap {
    width: 100%;
  }
}
#search-condition .container .panel-search-ope .cont .btn-wrap .btnBsc-search {
  width: 240px;
  height: 48px;
  margin: 0 auto;
  color: #e60012;
  font-size: 0.9rem;
  background-color: #fff;
  border: 1px solid #e60012;
}
#search-condition .container .panel-search-ope .cont .btn-wrap .btnBsc-search:hover {
  color: #fff;
  background-color: #e60012;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-search-ope .cont .btn-wrap .btnBsc-search {
    width: auto;
    padding: 0.5rem 2rem;
  }
}
#search-condition .container .panel-results {
  display: -ms-grid;
  display: grid;
  row-gap: 16px;
  padding: 2rem 3rem 2.2rem;
  background-color: #e0eaef;
  border-radius: 12px;
  -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-results {
    padding: 6vw 8vw 8vw;
  }
}
#search-condition .container .panel-results .num {
  width: 100%;
  max-width: 1080px;
  margin: 0 auto;
  font-size: 18px;
  font-weight: bold;
  text-align: left;
}
#search-condition .container .panel-results .num span {
  display: inline-block;
  margin: 0 0.3rem 0 0;
  font-weight: bold;
}
#search-condition .container .panel-results .condition {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  max-width: 1080px;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-results .condition {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#search-condition .container .panel-results .condition [class$=__label] {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  width: 140px;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-results .condition [class$=__label] {
    margin: 0 0 2vw;
  }
}
#search-condition .container .panel-results .condition [class$=__contents] {
  width: calc(100% - 140px);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-results .condition [class$=__contents] {
    width: 100%;
  }
}
#search-condition .container .panel-results .condition [class$=__contents] .filter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  width: calc(100% - 140px);
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
#search-condition .container .panel-results .condition [class$=__contents] .filter p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 0.5rem 0.5rem 0;
  padding: 0 0.2rem 0 1.1rem;
  background-color: #fff;
  border-radius: 20px;
}
#search-condition .container .panel-results .condition [class$=__contents] .filter p .cat-name {
  white-space: nowrap;
}
#search-condition .container .panel-results .condition [class$=__contents] .filter p .close {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 22px;
  height: 22px;
  margin: 0 0 0 0.5rem;
  padding: 0;
  font-size: 12px;
  border-radius: 20px;
  cursor: pointer;
}
@media screen and (max-width: 767px) {
  #search-condition .container .panel-results .condition [class$=__contents] .filter p .close {
    width: 18px;
    height: 18px;
    padding: 0 0 0 3px;
  }
}
#search-condition .container .panel-results .condition [class$=__contents] .filter p .close:hover {
  color: #fff;
  background-color: #444;
}
#search-condition .container .panel-results .condition [class$=__contents] .clear-conditions {
  width: auto;
}
#search-condition .container .panel-results .condition [class$=__contents] .clear-conditions button {
  color: #0084c1;
  font-size: 14.4px;
  background-color: transparent;
  text-decoration: none;
}
#search-condition .container .panel-results .condition [class$=__contents] .clear-conditions button:hover {
  opacity: 1;
  text-decoration: underline;
}

#list-auction {
  padding: 1rem;
}
@media screen and (max-width: 767px) {
  #list-auction {
    padding: 0;
  }
}
#list-auction .container {
  padding: 0 0 80px;
}
@media screen and (max-width: 767px) {
  #list-auction .container {
    padding: 4vw;
  }
}
#list-auction .container .auction-conteiner {
  margin: 0;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner {
    margin: 0;
  }
}
#list-auction .container .auction-conteiner:not(:first-of-type) {
  margin: 160px 0 0;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner:not(:first-of-type) {
    margin: 0;
  }
}
#list-auction .container .auction-conteiner .wrap-btn.add-favorite {
  width: 100%;
  margin: 1rem 0;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .wrap-btn.add-favorite {
    margin: 4vw 0;
  }
}
#list-auction .container .auction-conteiner .wrap-btn.add-favorite button.btnBsc-favorite {
  width: auto;
  margin: 0 0 0 auto;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .wrap-btn.add-favorite button.btnBsc-favorite {
    margin: 0 auto;
  }
}
#list-auction .container .auction-conteiner .wrap-btn.add-favorite button.btnBsc-favorite:hover {
  color: #444;
  background-color: #f5f5f5;
}
#list-auction .container .auction-conteiner .wrap-btn.add-favorite button.btnBsc-favorite img {
  width: 23px;
  height: auto;
  margin: 0 0.5rem 2px 0;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .wrap-btn.add-favorite button.btnBsc-favorite img {
    width: 3.8vw;
    margin: 0 1vw 2px 0;
  }
}
#list-auction .container .auction-conteiner .table-subtotal-total {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 1rem 0;
  padding: 1rem 110px 1rem 60px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #f7f7f7;
  border: 1px solid #ececec;
  border-radius: 8px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .table-subtotal-total {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 4vw;
    border: 1px solid #e3e3e3;
    border-radius: 4px;
  }
  #list-auction .container .auction-conteiner .table-subtotal-total.favorite {
    background-color: #e0eaef;
  }
  #list-auction .container .auction-conteiner .table-subtotal-total.favorite .price-subtotal .val {
    background-color: #d3e3eb;
    border: 1px solid #c2d6df;
  }
  #list-auction .container .auction-conteiner .table-subtotal-total.bid {
    background-color: #f9e2e4;
  }
  #list-auction .container .auction-conteiner .table-subtotal-total.bid .price-subtotal .val {
    background-color: #f6d5d8;
    border: 1px solid #e9c7ca;
  }
}
#list-auction .container .auction-conteiner .table-subtotal-total .auction-title {
  font-size: 14.4px;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .table-subtotal-total .auction-title {
    margin: 0 0 0.5rem;
    font-size: 4.5vw;
  }
}
#list-auction .container .auction-conteiner .table-subtotal-total .price-subtotal {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 14.4px;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .table-subtotal-total .price-subtotal {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}
#list-auction .container .auction-conteiner .table-subtotal-total .price-subtotal .label {
  display: inline-block;
  margin-right: 2rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .table-subtotal-total .price-subtotal .label {
    width: auto;
    margin-right: 1rem;
    font-size: 3.8vw;
  }
}
#list-auction .container .auction-conteiner .table-subtotal-total .price-subtotal .unit {
  font-size: 18px;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .table-subtotal-total .price-subtotal .unit {
    font-size: 4.6vw;
  }
}
#list-auction .container .auction-conteiner .table-subtotal-total .price-subtotal .val {
  display: inline-block;
  min-width: 140px;
  width: auto;
  padding: 9px 10px;
  font-size: 18px;
  font-weight: 500;
  text-align: right;
  background-color: #f3f3f3;
  border-radius: 4px;
  border: 1px solid #ececec;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .table-subtotal-total .price-subtotal .val {
    font-size: 4.8vw;
  }
}
#list-auction .container .auction-conteiner .wrap-btn.bulkbid {
  width: 100%;
  margin: 1rem 0;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .wrap-btn.bulkbid {
    margin: 4vw 0;
    text-align: center;
  }
}
#list-auction .container .auction-conteiner .wrap-btn.bulkbid button.bulkbid {
  width: auto;
  min-width: 280px;
  height: 60px;
  margin: 0 0 0 auto;
  color: #e60012;
  border: 1px solid #e60012;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .wrap-btn.bulkbid button.bulkbid {
    height: 14vw;
    margin: 0 auto;
  }
}
#list-auction .container .auction-conteiner .wrap-btn.bulkbid button.bulkbid:hover {
  color: #fff;
  background-color: #e60012;
}
#list-auction .container .auction-conteiner .auction-header {
  display: -ms-grid;
  display: grid;
  row-gap: 30px;
  margin: 0;
  padding: 3rem 1rem 4rem;
  border: 2px solid #e8e8e8;
  border-radius: 12px;
  background-image: url(../img/common/list_header_bg.png);
  background-position: center top;
  background-size: 100% auto;
  background-repeat: no-repeat;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header {
    row-gap: 5vw;
    width: 100%;
    margin: 10vw auto 0;
    padding: 8vw 4vw 7.5vw;
    background-size: cover;
  }
}
#list-auction .container .auction-conteiner .auction-header .ttl-auction {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  font-size: 28px;
  font-weight: bold;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .ttl-auction {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    font-size: 5vw;
  }
}
#list-auction .container .auction-conteiner .auction-header .ttl-auction span {
  display: inline-block;
  margin: 0.2rem 0 0.2rem 1rem;
  padding: 3px 16px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 4px;
  border: 2px solid #444;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .ttl-auction span {
    display: block;
    min-width: 50%;
    margin: 0.2rem auto 0;
    font-size: 3.5vw;
    text-align: center;
  }
}
#list-auction .container .auction-conteiner .auction-header .schedule {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  width: 100%;
  margin: 0 auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row {
    width: 100%;
    margin: 0 0 0.5rem;
    padding: 0;
  }
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row .country, #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont {
  padding: 3px;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .country, #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont {
    font-size: 3.8vw;
  }
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row .country span, #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont span {
  font-weight: 500;
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row .country {
  width: 38%;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .country {
    width: 28vw;
    text-align: left;
  }
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row .country .wic {
  position: relative;
  display: inline-block;
  width: 180px;
  padding: 0 35px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .country .wic {
    width: 100%;
    padding: 0 0 0 5vw;
    font-size: 3.5vw;
    font-weight: normal;
    line-height: 1.4;
  }
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row .country .wic:before {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 24px;
  height: 38px;
  background-image: url(../img/common/icn_schedule.svg);
  background-position: center 3px;
  background-size: 14px auto;
  background-repeat: no-repeat;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .country .wic:before {
    width: 3.5vw;
    height: 5.5vw;
    background-position: center 0.8vw;
    background-size: 3vw auto;
  }
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont {
  width: 62%;
  white-space: wrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont {
    width: calc(100% - 28vw);
    line-height: 1.4;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont span {
    font-weight: normal;
  }
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont .date {
  display: inline-block;
  margin: 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont .date {
    margin: 0 0.5rem 0 0;
  }
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont .time {
  display: inline-block;
  margin: 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont .time {
    margin: 0 0.5rem 0 0;
  }
}
#list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont .symbol {
  display: inline-block;
  margin: 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-header .schedule .sch-row .cont .symbol {
    margin: 0 0.5rem 0 0;
  }
}
#list-auction .container .auction-conteiner .auction-contents {
  margin: 20px auto;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents {
    margin: 4vw 0;
  }
}
#list-auction .container .auction-conteiner .auction-contents .status {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  margin: 0 0 0.3rem;
  padding: 0 0.5rem;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .status {
    margin: 0 0 0.3rem;
    padding: 0;
  }
}
#list-auction .container .auction-conteiner .auction-contents .status .num-results {
  width: 100%;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .status .num-results {
    margin: 0;
    padding: 0 0 0 1vw;
    font-size: 3.5vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .status .select-favorite {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .status .select-favorite {
    width: 100%;
  }
}
#list-auction .container .auction-conteiner .auction-contents .status .select-favorite input {
  width: auto;
  margin: 0 0.5rem;
  padding: 0;
  font-size: 16px;
  color: #0084c1;
  text-decoration: underline;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .status .select-favorite input {
    height: 24px;
    font-size: 3.5vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table {
  width: 100%;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table {
    margin: 0 0 4vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table {
  width: 100%;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table {
    border-bottom: none;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table thead {
  color: #fff;
  border-top-left-radius: 12px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table thead {
    display: none;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table thead tr th {
  width: auto;
  padding: 26px 10px;
  font-size: 13px;
  line-height: 1.2;
  text-align: center;
  vertical-align: middle;
  background-color: #444;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table thead tr th {
    font-size: 3vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table thead tr th.f-sm {
  padding: 26px 7px;
  font-size: 10px;
  line-height: 1.5;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table thead tr th:first-child {
  border-top-left-radius: 12px;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table thead tr th:last-child {
  border-top-right-radius: 12px;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table thead tr th.num {
  white-space: nowrap;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table thead tr th.ensure-w {
  width: 110px;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody {
  width: 100%;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr {
  display: table-row;
  border-bottom: 1px solid #fff;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr {
    display: block;
    max-width: 100%;
    margin: 0 auto 4vw;
    padding: 3vw 4vw;
    border: 1px solid #d9d9d9;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr:nth-child(odd) {
  background-color: #f5f5f5;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr:nth-child(even) {
  background-color: #eee;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr.is-hidden {
  display: none;
  opacity: 0;
  height: 0;
  margin: 0;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr:last-child {
  border-bottom: 7px solid #444;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td {
  min-width: 90px;
  padding: 11px 10px;
  font-size: 13px;
  text-align: center;
  line-height: 1.2;
  vertical-align: middle;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td {
    width: 100%;
    position: relative;
    display: block;
    padding: 8px 0;
    font-size: 3vw;
    text-align: right;
    border-bottom: 1px solid #e4e4e4;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td:before {
  position: absolute;
  left: 0;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.favorite-selected:before {
    content: "";
    bottom: calc(50% - 1.5vw);
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.maker {
  max-width: 140px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.maker {
    max-width: 100%;
    padding: 8px 0 8px 5em;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.maker:before {
    content: "";
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.date-bid {
  text-align: center;
  -webkit-box-flex: 2;
      -ms-flex-positive: 2;
          flex-grow: 2;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.date-bid {
    padding: 8px 0 8px 4em;
    text-align: right;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.date-bid:before {
    content: "";
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.itemname {
  padding: 11px 10px;
  text-align: left;
  -webkit-box-flex: 2;
      -ms-flex-positive: 2;
          flex-grow: 2;
  white-space: unset;
  word-break: break-word;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.itemname {
    padding: 8px 0 8px 4em;
    text-align: right;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.itemname:before {
    content: "";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.sim:before {
    content: "";
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.capacity {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.capacity:before {
    content: "";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.color:before {
    content: "";
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.grade {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.grade:before {
    content: "";
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.favorites {
  white-space: nowrap;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.favorites:before {
    content: "";
    font-weight: 400;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.quantity {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.quantity:before {
    content: "";
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.quantity-min {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.quantity-min:before {
    content: "";
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.price-min {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.price-min:before {
    content: "";
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.price-min .unit {
  font-family: "sans-serif", "system-ui";
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.current-price {
  white-space: nowrap;
  color: #e60012;
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.current-price {
    font-size: 3vw;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.current-price:before {
    content: "";
    color: #000;
    font-size: 3vw;
    font-weight: 400;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.current-price .unit {
  font-family: "sans-serif", "system-ui";
  font-weight: 600;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.winning-price {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.winning-price:before {
    content: "";
    font-weight: 400;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.winning-unit-price {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.winning-unit-price:before {
    content: "";
    font-weight: 400;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.bids {
  font-weight: 500;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.bids:before {
    content: "";
    font-weight: 400;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.number-bid span {
  white-space: nowrap;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.number-bid span.time {
  font-size: 0.9rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.number-bid:before {
    content: "";
    font-weight: 400;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.number-success-bid:before {
    content: "";
    font-weight: 400;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.time-remaining span {
  display: block;
  white-space: nowrap;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.time-remaining span {
    display: inline-block;
    text-align: right;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.time-remaining span.time {
  font-size: 0.9rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.time-remaining span.time {
    margin: 0 1vw;
    font-size: 3vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.time-remaining span.highest {
  color: #ff0000;
  font-size: 0.8rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.time-remaining span.highest {
    font-size: 3vw;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.time-remaining:before {
    content: "";
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.winning-total-price:before {
    content: "";
    font-weight: 400;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check {
  position: relative;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check:after {
  content: "";
  position: absolute;
  top: 15%;
  left: 0;
  width: 1px;
  height: 70%;
  background: #e3e3e3;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check:after {
    display: none;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check {
    border-bottom: none;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check:before {
    content: "";
    top: 10px;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check button.fav-mark {
  background: transparent;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check button.fav-mark .fav-pct {
  width: 24px;
  height: 24px;
  background-image: url(../img/common/icn_favorite_gr.svg);
  background-size: 24px 24px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check button.fav-mark .fav-pct {
    width: 5vw;
    height: 5vw;
    background-size: 5vw 5vw;
    -webkit-transform: translateY(-1vw);
            transform: translateY(-1vw);
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check button.fav-mark .fav-pct:hover {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check button.fav-mark .fav-pct:hover {
    -webkit-transform: translateY(-1vw);
            transform: translateY(-1vw);
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check button.fav-mark .fav-pct.added {
  background-image: url(../img/common/icn_favorite.svg);
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td.check button.fav-mark .fav-pct.added {
    width: 5vw;
    height: 5vw;
    background-size: 5vw 5vw;
    -webkit-transform: translateY(-1vw);
            transform: translateY(-1vw);
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td:last-child {
  border-bottom: none;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table tbody tr td a {
  color: #0084c1;
  font-weight: 500;
  line-height: 1.2;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .favorite-selected button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .favorite-selected button {
    margin: 0 0 0 auto;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .favorite-selected button img.trash {
  width: 28px;
  height: 28px;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .favorite-selected button span {
  font-size: 10px;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity {
  padding: 11px 5px 11px 10px;
  vertical-align: bottom;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity {
    padding: 8px 0;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field .bidp-label {
  margin: 0 0 0.3rem;
  text-align: center;
  white-space: nowrap;
  font-size: 10px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field .bidp-label {
    margin: 0;
    font-size: 3vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field .bidp-label .bid-quantity, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field .bidp-label .unit {
  font-size: 13px;
  font-weight: 500;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field input {
  width: 90px;
  height: 38px;
  text-align: right;
  color: #e60012;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field input {
    width: 110px;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field input.bid-placed::-webkit-input-placeholder {
  color: #ccc;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field input.bid-placed::-moz-placeholder {
  color: #ccc;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field input.bid-placed:-ms-input-placeholder {
  color: #ccc;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field input.bid-placed::-ms-input-placeholder {
  color: #ccc;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field input.bid-placed::placeholder {
  color: #ccc;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity .bidp-input-field .txt-error-w {
  display: none;
  max-height: 0;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity.error .bidp-input-field input {
  background-color: #fffac7;
  border: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity.error .bidp-input-field input {
    -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
            order: 3;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity.error .bidp-input-field .txt-error-w {
  position: relative;
  display: block;
  max-height: 1rem;
  margin: 0 0 0.2rem;
  color: #ff0000;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  -webkit-transform: translateX(-0.5rem);
          transform: translateX(-0.5rem);
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity.error .bidp-input-field .txt-error-w {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    margin: 0;
    padding-left: 1em;
    text-align: left;
    font-size: 3vw;
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity.error .bidp-input-field .txt-error-w.pl0 {
    padding-left: 0;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity.error .bidp-input-field .txt-error-w:after {
  position: absolute;
  display: inline-block;
  content: "";
  width: 14px;
  height: 14px;
  background: url(../img/common/icn_error.svg) no-repeat;
  background-size: contain;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-quantity.error .bidp-input-field .bidp-label {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
    margin: 0;
    font-size: 3vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price {
  padding: 11px 5px;
  vertical-align: bottom;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price {
    padding: 8px 0;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap .bidp-label {
  margin: 0 0 0.3rem;
  white-space: nowrap;
  text-align: center;
  font-size: 10px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap .bidp-label {
    margin: 0;
    font-size: 3vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap .bidp-label .bid-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap .bidp-label .unit {
  font-size: 13px;
  font-weight: 500;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap .bidp-input-field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap .bidp-input-field .unit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  font-family: "sans-serif", "system-ui";
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap .bidp-input-field .unit {
    font-size: 3.8vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap .bidp-input-field input {
  width: 110px;
  height: 38px;
  text-align: right;
  color: #e60012;
  font-weight: 500;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price .bidp-wrap .txt-error-w {
  display: none;
  max-height: 0;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price.error .bidp-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price.error .bidp-wrap .bidp-input-field {
    -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
            order: 3;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price.error .bidp-wrap .bidp-input-field input {
  background-color: #fffac7;
  border: 1px solid #ccc;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price.error .bidp-wrap .txt-wrap {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price.error .bidp-wrap .txt-error-w {
  position: relative;
  display: block;
  max-height: 1rem;
  margin: 0 0 0.2rem;
  color: #ff0000;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  -webkit-transform: translateX(-0.5rem);
          transform: translateX(-0.5rem);
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price.error .bidp-wrap .txt-error-w {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
    margin: 0;
    padding-left: 1em;
    font-size: 3vw;
    text-align: left;
    white-space: nowrap;
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price.error .bidp-wrap .txt-error-w.pl0 {
    padding-left: 0;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price.error .bidp-wrap .txt-error-w:after {
  position: absolute;
  display: inline-block;
  content: "";
  width: 14px;
  height: 14px;
  background: url(../img/common/icn_error.svg) no-repeat;
  background-size: contain;
  background-position: center;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .my-bid-price.error .bidp-wrap .bidp-label {
    -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
            order: 1;
    margin: 0;
    padding: 0;
    font-size: 3vw;
    text-align: left;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal {
  padding: 11px 5px;
  vertical-align: bottom;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal {
    padding: 8px 0;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap .bidp-label {
  margin: 0 0 0.3rem;
  white-space: nowrap;
  text-align: center;
  font-size: 10px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap .bidp-label {
    margin: 0;
    font-size: 3vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap .bidp-label .bid-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap .bidp-label .unit {
  font-size: 13px;
  font-weight: 500;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap .bidp-input-field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap .bidp-input-field .unit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  font-family: "sans-serif", "system-ui";
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap .bidp-input-field .unit {
    font-size: 3.8vw;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .subtotal .bidp-wrap .bidp-input-field .price-subtotal {
  width: 110px;
  height: 38px;
  padding: 9px 10px;
  font-size: 16px;
  font-weight: 500;
  text-align: right;
  background-color: transparent;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap {
  max-width: 110px;
  padding: 11px 10px 11px 5px;
  vertical-align: bottom;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    max-width: 100%;
    padding: 8px 0;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap button {
  font-weight: 500;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap button {
    font-size: 3.5vw;
    line-height: 1;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap button.bid {
  width: 100%;
  height: 38px;
  margin: 0;
  padding: 0.55rem;
  color: #fff;
  line-height: 1;
  background-color: #e60012;
  border: 1px solid #e60012;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap button.bid {
    width: calc(50% - 0.5rem);
    margin: 0;
    padding: 0.6rem 1rem;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap button.bid:hover {
  opacity: 0.7;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap button.clear {
  margin: 0 0 0.3rem;
  font-size: 13px;
  color: #0084c1;
  line-height: 1;
  text-decoration: none;
  background-color: transparent;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap button.clear {
    margin: 0;
    width: calc(50% - 0.5rem);
    height: 38px;
    padding: 0.6rem 1rem;
    text-decoration: none;
    background-color: #e1f0f7;
    border: 1px solid #0084c1;
    border-radius: 4px;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list tbody tr .bidding-wrap button.clear:hover {
  opacity: 1;
  text-decoration: underline;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr {
  background-color: #f7f7f7;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.quantity-min, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.price-min, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.favorites, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.bids, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.current-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.time-remaining {
  background-color: #edf2f4;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.quantity-min, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.price-min, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.favorites, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.bids, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.current-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.time-remaining {
    background-color: transparent;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.my-bid-quantity, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.my-bid-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.bidding-wrap, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.subtotal {
  background-color: #e0eaef;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.my-bid-quantity, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.my-bid-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.bidding-wrap, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.favorite tbody tr td.subtotal {
    background-color: transparent;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr {
  background-color: #f7f7f7;
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.quantity-min, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.price-min, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.favorites, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.current-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.bids, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.time-remaining {
  background-color: #faf1f2;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.quantity-min, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.price-min, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.favorites, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.current-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.bids, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.time-remaining {
    background-color: transparent;
  }
}
#list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.my-bid-quantity, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.my-bid-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.bidding-wrap, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.subtotal {
  background-color: #f9e2e4;
}
@media screen and (max-width: 767px) {
  #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.my-bid-quantity, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.my-bid-price, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.bidding-wrap, #list-auction .container .auction-conteiner .auction-contents .list-item-table table.mypage-list.bidding tbody tr td.subtotal {
    background-color: transparent;
  }
}
#list-auction .container .fix-bottom-contents {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 90px;
  margin: 0;
  padding: 1rem;
  background-color: #fff;
  border-top: 1px solid #ccc;
  z-index: 2;
}
@media screen and (max-width: 767px) {
  #list-auction .container .fix-bottom-contents {
    height: auto;
    padding: 13px 4vw;
  }
}
#list-auction .container .fix-bottom-contents .cont-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 1452px;
  max-width: 100%;
  margin: 0 auto;
  padding-right: 86px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .fix-bottom-contents .cont-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding-right: 0;
  }
}
#list-auction .container .fix-bottom-contents .cont-wrap p {
  padding: 0 1rem;
  font-size: 14px;
}
@media screen and (max-width: 767px) {
  #list-auction .container .fix-bottom-contents .cont-wrap p {
    display: none;
  }
}
#list-auction .container .fix-bottom-contents .cont-wrap .btnBsc-favorite {
  width: auto;
  margin: 0;
}
@media screen and (max-width: 767px) {
  #list-auction .container .fix-bottom-contents .cont-wrap .btnBsc-favorite {
    margin: 0;
    padding: 0 4vw;
    font-size: 3.3vw;
  }
}
#list-auction .container .fix-bottom-contents .cont-wrap .btnBsc-favorite:hover {
  background-color: #f4f4f4;
  opacity: 1;
}
#list-auction .container .fix-bottom-contents .cont-wrap .btnBsc-favorite img {
  width: 23px;
  height: auto;
  margin: 0 0.5rem 2px 0;
}
@media screen and (max-width: 767px) {
  #list-auction .container .fix-bottom-contents .cont-wrap .btnBsc-favorite img {
    width: 3.8vw;
    margin: 0 1vw 2px 0;
  }
}
#list-auction .container .fix-bottom-contents .cont-wrap .btnBsc-bulkbid {
  width: auto;
  min-width: 280px;
  margin: 0;
  color: #e60012;
  background-color: #fff;
  border: 1px solid #e60012;
}
@media screen and (max-width: 767px) {
  #list-auction .container .fix-bottom-contents .cont-wrap .btnBsc-bulkbid {
    min-width: 50%;
  }
}
#list-auction .container .fix-bottom-contents .cont-wrap .btnBsc-bulkbid:hover {
  color: #fff;
  background-color: #e60012;
}
/*# sourceMappingURL=list.css.map */
