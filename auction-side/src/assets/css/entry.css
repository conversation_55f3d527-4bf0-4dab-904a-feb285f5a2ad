@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *会員登録
 * *********************************************************************** */
#main #entry-info {
  margin: 60px 0 60px;
  padding: 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #entry-info {
    margin-top: 40px;
  }
}
#main #entry-info dl {
  border: 1px solid #bebebe;
  background-color: #efefef;
  padding: 50px 50px 40px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl {
    padding: 15px 15px 20px;
  }
}
#main #entry-info dl dt {
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  color: #e60012;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dt {
    font-size: 17px;
    text-align: left;
  }
}
#main #entry-info dl dd {
  margin-top: 30px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd {
    margin-top: 20px;
  }
}
#main #entry-info dl dd p.jp-document {
  font-weight: 700;
  text-align: center;
  font-size: 18px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd p.jp-document {
    font-size: 1rem;
  }
}
#main #entry-info dl dd ol {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol {
    display: block;
  }
}
#main #entry-info dl dd ol li {
  width: 300px;
  background-color: #fff;
  margin-top: 20px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li {
    width: 100% !important;
    padding-bottom: 30px;
  }
}
#main #entry-info dl dd ol li:nth-of-type(2) {
  width: 380px;
}
#main #entry-info dl dd ol li a {
  display: block;
  text-align: center;
  width: 100%;
  height: 100%;
  padding: 0 15px 20px;
}
#main #entry-info dl dd ol li p.entry-info-item {
  position: relative;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  padding-top: 25px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li p.entry-info-item {
    letter-spacing: 0;
    font-size: 1rem;
  }
}
#main #entry-info dl dd ol li p.entry-info-item::after {
  content: "?";
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  color: #e60012;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #e60012;
  line-height: 18px;
  text-align: center;
}
#main #entry-info dl dd ol li p.entry-info-item span {
  display: inline-block;
  padding-top: 5px;
  padding-left: 18px;
  font-size: 24px;
  color: #fff;
  font-weight: 700;
  position: absolute;
  top: 0;
  left: -15px;
  z-index: 0;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li p.entry-info-item span {
    font-size: 20px;
    padding-top: 6px;
    padding-left: 16px;
  }
}
#main #entry-info dl dd ol li p.entry-info-item span::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  border-top: 40px solid #e60012;
  border-right: 40px solid transparent;
  border-bottom: 40px solid transparent;
  border-left: 40px solid #e60012;
  z-index: -1;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li p.entry-info-item span::before {
    border-width: 35px;
  }
}
#main #entry-info dl dd ol li img {
  display: inline-block;
  margin-top: 5px;
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li img {
    height: 130px;
  }
}
#main #entry-info dl dd ol li a:hover img {
  opacity: 0.8;
}
#main #entry-info dl dd ol li p.entry-info-note {
  color: #f00;
  text-align: center;
  margin-top: 5px;
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li p.entry-info-note {
    margin-bottom: -20px;
    font-size: 14px;
  }
}
#main #entry-info dl dd p.kojin {
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd p.kojin {
    text-align: left;
    text-indent: -1em;
    padding-left: 1em;
  }
}
#main #entry-info dl dd .entry-info-btn {
  margin: 40px auto 0;
  width: 360px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd .entry-info-btn {
    width: 100%;
    margin-top: 20px;
  }
}
#main #entry-info p.entry-info-att {
  margin-top: 30px;
  font-size: 16px;
}
#main #entry-info p.entry-info-att a {
  text-decoration: underline;
  color: #e60012;
}
#main #entry-form {
  width: 1280px;
  max-width: calc(100% - 2rem);
  margin: 60px auto 2rem;
  padding: 70px 120px 80px;
  background-color: #f7f7f7;
}
@media screen and (max-width: 1080px) {
  #main #entry-form {
    padding: 70px 30px 80px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form {
    margin: 40px auto 20px;
    padding: 20px 15px 25px;
  }
}
#main #entry-form p.entry-form-info {
  text-align: center;
  font-weight: 500;
  font-size: 1.1rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form p.entry-form-info {
    text-align: left;
    font-size: 0.9rem;
  }
}
#main #entry-form em.req {
  display: inline-block;
  background-color: #ef4553;
  width: auto;
  height: auto;
  padding: 2px 5px;
  color: #fff;
  font-weight: 700;
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
  border-radius: 2px;
}
#main #entry-form p.entry-form-info em.req {
  margin-right: 5px;
  border-radius: 2px;
  position: relative;
  top: -2px;
}
#main #entry-form table.tbl-entry {
  width: 100%;
  margin: 0 auto;
}
#main #entry-form p.entry-form-info + table.tbl-entry {
  margin-top: 40px;
}
#main #entry-form table.tbl-entry {
  width: 100%;
}
#main #entry-form table.tbl-entry tr {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0.3rem 0;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main #entry-form table.tbl-entry tr.required-field {
  background-color: #faecec;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.required-field {
    padding: 0 4vw;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.required-field th.pt-lg {
    padding: 0 4vw;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.required-field td {
    padding: 6px 4vw 20px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.required-field.info th {
    padding: 26px calc(4vw - 0.5rem) 5px;
  }
}
#main #entry-form table.tbl-entry tr.required-field.info td {
  padding: 26px 0 15px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.required-field.info td {
    padding: 0 4vw 30px;
  }
}
#main #entry-form table.tbl-entry tr.required-field.end td {
  padding: 15px 0 25px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.required-field.end td {
    padding: 6px 4vw 25px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr.line-break th.pt-lg {
    padding: 20px 0 0.3rem;
  }
}
#main #entry-form table.tbl-entry tr th {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  font-weight: 700;
  vertical-align: top;
  width: 310px;
  position: relative;
  padding: 15px 2rem 15px;
  line-height: 1.2;
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry tr th {
    width: 230px;
    min-width: 190px;
    padding: 0 1rem;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th {
    display: block;
    width: 100%;
    padding: 0;
    font-size: 3.5vw;
  }
}
#main #entry-form table.tbl-entry tr th.pt-lg {
  padding: 27px 2rem 15px;
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry tr th.pt-lg {
    padding: 0 1rem;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th.pt-lg {
    padding: 0;
  }
}
#main #entry-form table.tbl-entry tr th em.req {
  position: absolute;
  top: 36px;
  right: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th em.req {
    position: static;
    display: inline-block;
    margin: 0 0 0 0.5rem;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
#main #entry-form table.tbl-entry tr td {
  padding: 15px 0;
  position: relative;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td {
    display: block;
    width: 100%;
    padding: 6px 0 20px;
  }
}
#main #entry-form table.tbl-entry tr td p.privacy-link {
  font-size: 14.4px;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td p.privacy-link {
    margin-top: -30px;
    font-size: 3vw;
  }
}
#main #entry-form table.tbl-entry tr td p.privacy-link a {
  text-decoration: underline;
  color: #444;
}
#main #entry-form table.tbl-entry tr td p.privacy-link a:hover {
  color: #e60012;
}
#main #entry-form table.tbl-entry tr td.agree {
  padding: 23px 0 15px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td.agree {
    padding: 12px 0 20px;
  }
}
#main #entry-form table.tbl-entry tr td .checkbox-parts {
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td .checkbox-parts {
    font-size: 3.8vw;
  }
}
#main.comp #entry-form table.tbl-entry td, #main[class^=contact-] #entry-form table.tbl-entry td {
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main.comp #entry-form table.tbl-entry td, #main[class^=contact-] #entry-form table.tbl-entry td {
    font-size: 3vw;
  }
}
#main.comp #entry-form table.tbl-entry tr.att-use {
  display: none;
}
#main #entry-form table.tbl-entry th, #main #entry-form table.tbl-entry td {
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry th, #main #entry-form table.tbl-entry td {
    font-size: 3vw;
  }
}
#main #entry-form table.tbl-entry th.ttl-agree, #main #entry-form table.tbl-entry td.ttl-agree {
  padding: 15px 2rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry th.ttl-agree, #main #entry-form table.tbl-entry td.ttl-agree {
    padding: 0;
    margin: 0 0 0.5rem;
  }
}
#main #entry-form table.tbl-entry th.phone, #main #entry-form table.tbl-entry td.phone {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
#main #entry-form table.tbl-entry p .ipt-wrap {
  display: inline-block;
  margin: 0 2rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry p .ipt-wrap {
    margin: 0 1rem 0 0;
  }
}
#main #entry-form table.tbl-entry .ipt-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-wrap {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
#main #entry-form table.tbl-entry .ipt-wrap input.zip-search {
  margin: 0 0 0 15px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-wrap input.zip-search {
    width: 80px;
    padding-left: 5px;
    padding-right: 5px;
    margin: 0 0 0 15px;
  }
}
#main #entry-form table.tbl-entry .ipt-wrap input.zip-search::-webkit-input-placeholder {
  font-size: 14.4px;
}
#main #entry-form table.tbl-entry .ipt-wrap input.zip-search::-moz-placeholder {
  font-size: 14.4px;
}
#main #entry-form table.tbl-entry .ipt-wrap input.zip-search:-ms-input-placeholder {
  font-size: 14.4px;
}
#main #entry-form table.tbl-entry .ipt-wrap input.zip-search::-ms-input-placeholder {
  font-size: 14.4px;
}
#main #entry-form table.tbl-entry .ipt-wrap input.zip-search::placeholder {
  font-size: 14.4px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-wrap input.zip-search::-webkit-input-placeholder {
    font-size: 3.5vw;
  }
  #main #entry-form table.tbl-entry .ipt-wrap input.zip-search::-moz-placeholder {
    font-size: 3.5vw;
  }
  #main #entry-form table.tbl-entry .ipt-wrap input.zip-search:-ms-input-placeholder {
    font-size: 3.5vw;
  }
  #main #entry-form table.tbl-entry .ipt-wrap input.zip-search::-ms-input-placeholder {
    font-size: 3.5vw;
  }
  #main #entry-form table.tbl-entry .ipt-wrap input.zip-search::placeholder {
    font-size: 3.5vw;
  }
}
#main #entry-form table.tbl-entry .ipt-wrap span.int-no {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background-color: #F7F7F7;
  border: 1px solid #E3E3E3;
  border-right: none;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  width: 55px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-wrap span.int-no {
    width: calc(100% - 55px) !important;
    font-size: 3.8vw;
  }
}
#main #entry-form table.tbl-entry .ipt-wrap span.ipt-rule {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 13px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-wrap span.ipt-rule {
    width: calc(100% - 55px) !important;
    margin-left: 0;
    margin-top: 3px;
    font-size: 3vw;
  }
}
#main #entry-form table.tbl-entry .ipt-wrap p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  margin: 0 0.5rem;
}
#main #entry-form table.tbl-entry .ipt-wrap p span {
  display: inline-block;
  margin: 0 0.5rem 0 0.7rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-wrap p span {
    margin: 0 0.3rem 0 0.5rem;
  }
}
#main #entry-form table.tbl-entry .ipt-wrap p.mb {
  margin: 0 0 0.2rem;
}
#main #entry-form table.tbl-entry .phone-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 460px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry .phone-wrap {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    width: 360px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .phone-wrap {
    width: 100%;
  }
}
#main #entry-form table.tbl-entry .phone-wrap .code-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 150px;
  margin-right: 10px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .phone-wrap .code-wrap {
    width: 100%;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}
#main #entry-form table.tbl-entry .phone-wrap .code-wrap .label-country-code {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 11px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .phone-wrap .code-wrap .label-country-code {
    margin-right: 10px;
    font-size: 2.4vw;
  }
}
#main #entry-form table.tbl-entry .phone-wrap .code-wrap input.country-code {
  width: 100px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .phone-wrap .code-wrap input.country-code {
    width: 160px;
  }
}
#main #entry-form table.tbl-entry .phone-wrap input.phone-number {
  width: 300px;
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry .phone-wrap input.phone-number {
    width: 100%;
    margin-top: 10px;
  }
}
#main #entry-form table.tbl-entry .ipt-rule {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 13px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-rule {
    width: calc(100% - 55px) !important;
    margin-left: 0;
    margin-top: 3px;
    font-size: 3vw;
  }
}
#main #entry-form table.tbl-entry .select-style {
  width: 360px;
  max-width: 100%;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .select-style {
    width: 100%;
    max-width: 100%;
  }
}
#main #entry-form table.tbl-entry .select-style.iptW-SS {
  width: 140px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .select-style.iptW-SS {
    width: 104px;
  }
}
#main #entry-form table.tbl-entry .select-style select.iptW-SS {
  width: 140px;
  padding: 11px 20px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .select-style select.iptW-SS {
    width: 104px;
    margin: 0 0 0.5rem;
  }
}
#main #entry-form table.tbl-entry .select-col {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
#main #entry-form table.tbl-entry .select-col select {
  height: 48px;
  margin: 0 5px 5px 0;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .select-col select {
    height: 7vw;
    min-height: 48px;
  }
}
#main #entry-form table.tbl-entry input {
  height: 48px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry input {
    height: 7vw;
    min-height: 48px;
  }
}
#main #entry-form table.tbl-entry input.iptW-M {
  width: 460px;
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry input.iptW-M {
    width: 360px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry input.iptW-M {
    width: 100%;
    max-width: 100%;
  }
}
#main #entry-form table.tbl-entry input.iptW-MM {
  width: 300px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry input.iptW-MM {
    width: 305px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry input.iptW-MM {
    width: calc(100% - 55px);
  }
}
#main #entry-form table.tbl-entry input.iptW-S {
  width: 184px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry input.iptW-S {
    width: 138px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry input.iptW-S {
    width: calc(50% - 8vw);
  }
}
#main #entry-form table.tbl-entry input.iptW-SS {
  width: 140px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry input.iptW-SS {
    width: 140px;
  }
}
#main #entry-form table.tbl-entry input.mb {
  margin: 0 0 0.2rem;
}
#main #entry-form table.tbl-entry textarea {
  width: 460px;
  height: 200px;
  resize: vertical;
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry textarea {
    width: 360px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry textarea {
    width: 100%;
  }
}
#main #entry-form table.tbl-entry .ipt-file-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-file-box {
    display: block;
  }
}
#main #entry-form table.tbl-entry .ipt-file-box label {
  margin-right: 10px;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-file-box label {
    margin-right: 0;
    margin-bottom: 5px;
  }
}
#main #entry-form table.tbl-entry .ipt-file-box .ipt-file-txt {
  margin: 0 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .ipt-file-box .ipt-file-txt {
    margin: 4vw 0 0 15px;
  }
}
#main #entry-form table.tbl-entry .file-up-att {
  margin-top: -20px;
  font-size: 14.4px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .file-up-att {
    margin-top: -40px;
    font-size: 3.5vw;
  }
}
#main #entry-form table.tbl-entry .file-up-desc {
  margin: 15px 0 0 15px;
  font-size: 14.4px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry .file-up-desc {
    margin: 4vw 0 4vw 15px;
    font-size: 3vw;
  }
}
#main #entry-form .btn-form {
  margin-top: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
#main.comp p.comp-msg {
  margin: 0 0 60px;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main.comp p.comp-msg {
    margin: 0 0 40px;
    text-align: left;
    font-size: 0.9rem;
  }
}
#main.contact-used #target-item {
  width: 1280px;
  max-width: calc(100% - 2rem);
  margin: 60px auto 0;
  margin-top: 60px;
  margin-bottom: -30px;
  border: 1px solid #EAEAEA;
}
#main.contact-used #target-item ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
#main.contact-used #target-item ul li + li {
  border-top: none;
}
#main.contact-used #target-item ul li .target-item-txt {
  width: 100%;
  padding: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  letter-spacing: 0;
}
#main.contact-used #target-item ul li .target-item-txt p.target-item-no {
  background-color: #000;
  color: #fff;
  font-weight: 700;
  line-height: 1;
  display: inline-block;
  padding: 6px 10px 8px;
  margin-right: 10px;
}
#main.contact-used #target-item ul li .target-item-txt p.target-item-system {
  color: #01a7ac;
  font-size: 14px;
  font-weight: 700;
  background-color: #E8FEFF;
  border: 1px solid #01A7AC;
  border-radius: 100vh;
  padding: 2px 15px 0;
}
#main.contact-used #target-item ul li .target-item-txt .target-item-data {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
#main.contact-used #target-item ul li .target-item-txt .target-item-data dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-width: 33.3333333333%;
  margin: 0;
  padding-right: 15px;
}
@media screen and (max-width: 767px) {
  #main.contact-used #target-item ul li .target-item-txt .target-item-data dl {
    margin: 0;
    padding: 0;
  }
}
#main.contact-used #target-item ul li .target-item-txt .target-item-data dl dt, #main.contact-used #target-item ul li .target-item-txt .target-item-data dl dd {
  padding: 6px 10px;
}
#main.contact-used #target-item ul li .target-item-txt .target-item-data dl dt {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-width: 8em;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  font-size: 14px;
  font-weight: 600;
  color: #bb0000;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  #main.contact-used #target-item ul li .target-item-txt .target-item-data dl dt {
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
    min-width: 8em;
    padding: 6px 0;
    font-size: 3vw;
  }
}
#main.contact-used #target-item ul li .target-item-txt .target-item-data dl dd {
  min-width: 10em;
  font-size: 15px;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  #main.contact-used #target-item ul li .target-item-txt .target-item-data dl dd {
    font-size: 3.5vw;
  }
}
/*# sourceMappingURL=entry.css.map */