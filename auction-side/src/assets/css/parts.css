@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *共通パーツ
 * *********************************************************************** */
/* アニメーション
 * *========================================== */
@-webkit-keyframes fade-basic {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fade-basic {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
/* ドル記号
 * *========================================== */
.unit {
  font-family: "sans-serif", "system-ui";
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *ボタン
 * *********************************************************************** */
/* 基本形ボタン
 * *========================================== */
btn {
  cursor: pointer;
}

btn:hover {
  opacity: 0.8;
}

[class^=btnBsc-] {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 280px;
  height: 60px;
  margin: 0 auto;
  padding: 0;
  font-weight: bold;
  border-radius: 4px;
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
@media screen and (max-width: 767px) {
  [class^=btnBsc-] {
    width: 80vw;
    height: 14vw;
  }
}
[class^=btnBsc-]:hover {
  opacity: 0.8;
}
[class^=btnBsc-] img {
  display: inline-block;
}

/* リスト下ボタン/もっと見るボタン */
main section .wrap-btn button {
  position: relative;
  width: 280px;
  max-width: 100%;
  height: 60px;
  margin: 0 auto;
  padding: 0.5rem 2rem;
  font-size: 14.4px;
  font-weight: 500;
  background-color: #fff;
  border: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  main section .wrap-btn button {
    height: 14vw;
    font-size: 3.5vw;
  }
}
main section .wrap-btn button .arrow {
  position: absolute;
  top: calc(50% - 9px);
  right: 12px;
  width: 18px;
  height: 18px;
}
main section .wrap-btn button .arrow:after {
  position: absolute;
  top: calc(50% - 7px);
  right: 0.5px;
  color: #ccc;
  font-size: 1rem;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 1;
  -webkit-transform: rotateZ(90deg);
          transform: rotateZ(90deg);
}
main section .wrap-btn button:hover {
  color: #444;
  background-color: #f5f5f5;
}
main section .wrap-btn.list-more {
  width: 100%;
  margin: 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  main section .wrap-btn.list-more {
    margin: 0 0 4vw;
  }
}
main section .wrap-btn.list-more .btn {
  width: 100%;
  height: auto;
  padding: 1.2rem 2rem 1.3rem;
  color: #fff;
  background-color: #444;
  border: 1px solid #444;
  border-top-left-radius: 0;
  border-bottom-left-radius: 12px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 12px;
}
@media screen and (max-width: 767px) {
  main section .wrap-btn.list-more .btn {
    border-radius: 4px;
  }
}
@media screen and (max-width: 767px) {
  main section .wrap-btn.list-more .btn:hover {
    opacity: 1;
  }
}
main section .wrap-btn.list-more .btn.newslist-more {
  width: 340px;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  main section .wrap-btn.list-more .btn.newslist-more {
    width: 100%;
  }
}
main section .wrap-btn.list-more .btn .txt {
  position: relative;
  display: inline-block;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  main section .wrap-btn.list-more .btn .txt {
    font-size: 3.8vw;
  }
}
main section .wrap-btn.list-more .btn .txt .arrow {
  position: absolute;
  top: calc(50% - 10px);
  right: -20px;
  width: 18px;
  height: 18px;
}
main section .wrap-btn.list-more .btn .txt .arrow:after {
  position: absolute;
  top: calc(50% - 7px);
  right: 0.5px;
  color: #fff;
  font-size: 1rem;
  font-weight: 900;
  font-family: "Material Icons";
  content: "\e5cc";
  line-height: 1;
  -webkit-transform: rotateZ(90deg);
          transform: rotateZ(90deg);
}
main section .wrap-btn.list-more.is-hidden {
  display: none;
}

/* 退会ボタン */
button.withdraw {
  width: 240px;
  height: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 auto;
  color: #000;
  font-size: 1rem;
  font-weight: 700;
  background-color: #d3d3d3;
  border-radius: 4px;
}

/* ---------------------------
 * *ダウンロードボタン
 * *----------------------------- */
.btnBsc-DL {
  background-color: #e60012;
}
.btnBsc-DL img {
  width: 24px;
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  .btnBsc-DL img {
    width: 18px;
  }
}

/* ---------------------------
 * *黒（濃グレー）ボタン
 * *----------------------------- */
.btnBsc-Black {
  color: #fff;
  background-color: #444;
  line-height: 1;
}
.btnBsc-Black img {
  width: 19px;
  position: relative;
  top: -2px;
  margin-left: 15px;
}
@media screen and (max-width: 767px) {
  .btnBsc-Black img {
    width: 13px;
    top: -1px;
  }
}

/* ---------------------------
 * *色ボタン（コーポレートカラー）
 * *----------------------------- */
.btnBsc-CoCor {
  background-color: #e60012;
}
.btnBsc-CoCor img {
  width: 19px;
  position: relative;
  top: -2px;
  margin-left: 15px;
}
@media screen and (max-width: 767px) {
  .btnBsc-CoCor img {
    width: 13px;
    top: -1px;
  }
}

/* ---------------------------
 * *アクションボタン（会員登録など）
 * *----------------------------- */
.btn-form {
  width: 100%;
  text-align: center;
  margin: 0 0 60px;
  padding: 1rem 1rem;
}
@media screen and (max-width: 767px) {
  .btn-form {
    margin: 0 0 40px;
  }
}

/* ---------------------------
 * *リンク
 * *----------------------------- */
a.link-std {
  color: #e60012;
  text-decoration: underline;
}
a.link-std:hover {
  text-decoration: none;
  opacity: 1;
}

.flex-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *お気に入りマーク
 * *********************************************************************** */
.com-item-box {
  display: inline-block;
  position: relative;
}
.com-item-box .fav-mark {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 70px;
  height: auto;
  margin: 0 0 0 1rem;
  padding: 2px 5px;
  text-align: center;
  border: 1px solid #e3e3e3;
  background-color: #fff;
}
.com-item-box .fav-mark:hover {
  opacity: 0.7;
}
@media screen and (max-width: 767px) {
  .com-item-box .fav-mark {
    padding: 2px 5px;
  }
}
.com-item-box .fav-mark .fav-pct {
  width: 30px;
  height: 30px;
  margin: 0 auto;
  padding: 2px;
  background: url("../img/common/icn_favorite.svg");
  background-size: 28px auto;
  background-position: 50% 50%;
  background-clip: content-box;
}
.com-item-box .fav-mark .fav-pct.active {
  background: url("../img/common/icn_favorite_on.svg");
  background-size: 28px auto;
  background-position: 50% 50%;
  background-clip: content-box;
}
.com-item-box .fav-mark span {
  display: block;
  width: 100%;
  color: #444;
  font-size: 10px;
  text-align: center;
}
.com-item-box .fav-mark:hover {
  cursor: pointer;
}

/* ログイン前は非表示 */
body.state-out span.fav-mark {
  display: none !important;
}
body.item_p-detail #terms.com-item-box {
  display: block;
}
body.item_p-detail #terms.com-item-box span.fav-mark {
  width: 40px;
  height: 40px;
  right: 20px;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *見出し
 * *********************************************************************** */
#main h1 {
  width: 100%;
  margin: 3rem 0;
  padding: 0 1rem;
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #main h1 {
    margin: 12vw 0;
    padding: 0;
    font-size: 5.8vw;
  }
}
#main h1.mb0 {
  margin-bottom: 0;
}
#main h1 span {
  display: block;
  width: 100%;
  height: auto;
  margin: 0 auto;
  padding: 0;
  text-align: center;
}
#main h1 span .type {
  display: inline-block;
  width: auto;
  margin: 0 auto;
  padding: 0.4rem 1.2rem;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  border-radius: 4px;
  border: 2px solid #444;
}
#main h2 {
  width: 100%;
  margin: 0;
  padding: 0 1rem;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main h2 {
    padding: 0;
    font-size: 5.2vw;
  }
}
#main h2.mt40 {
  margin-top: 40px;
}
#main h2.mb5 {
  margin-bottom: 5px;
}
#main h2 .ttl {
  width: 100%;
  text-align: center;
  margin: 0 0 0.4rem;
  padding: 0;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: 2px;
}
@media screen and (max-width: 767px) {
  #main h2 .ttl {
    margin: 0 0 0.5rem;
    font-size: 1.7rem;
  }
}
#main h2 .sub {
  font-size: 1.2rem;
  text-align: center;
  letter-spacing: 2px;
  font-weight: 300;
  font-family: "Noto Sans", sans-serif;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main h2 .sub {
    font-size: 1rem;
  }
}
#main h2 .ttl.specified {
  font-size: 1.4rem;
}
@media screen and (max-width: 767px) {
  #main h2 .ttl.specified {
    font-size: 1.2rem;
  }
}
#main h3 {
  width: 100%;
  max-width: 100%;
  margin: 1.5rem auto 1.5rem;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 1.2;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #main h3 {
    font-size: 1.4rem;
    text-align: center;
  }
}
#main h3.p0 {
  padding-right: 0;
  padding-left: 0;
}
#main h3.p1 {
  padding-right: 1rem;
  padding-left: 1rem;
}
#main .desc-an {
  text-align: center;
  font-weight: 500;
}

#sample {
  padding: 100px 0;
}

#main .place-modal {
  position: relative;
  width: 100%;
  padding: 1rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main .place-modal {
    padding: 1rem;
  }
}
#main .place-modal .btn.modal-open {
  width: 280px;
  max-width: 100%;
  height: 56px;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #e60012;
  border-radius: 4px;
}

#main .list-slider {
  padding: 2rem 0 3.5rem;
}
@media screen and (max-width: 767px) {
  #main .list-slider {
    padding: 1rem calc(1rem - 7.5px) 2.5rem;
  }
}
#main .list-slider .content-wrap {
  width: calc(1280px + 2rem);
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: center;
}
#main .list-slider .content-wrap ul.list-item-gallery {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  margin: 0 auto;
  padding: 0 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery {
    width: auto;
    overflow: hidden;
    padding: 0;
  }
}
#main .list-slider .content-wrap ul.list-item-gallery li {
  margin: 0 15px 15px 0;
  background-color: #fff;
  border: 1px solid #e5e5e5;
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery li {
    width: auto;
    margin: 0 7.5px 15px;
  }
}
#main .list-slider .content-wrap ul.list-item-gallery li.soldout a {
  position: relative;
}
#main .list-slider .content-wrap ul.list-item-gallery li.soldout a figure:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  background-image: url(../img/common/icn_soldout.png);
  background-size: 92% auto;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;
}
#main .list-slider .content-wrap ul.list-item-gallery li a {
  display: block;
  height: 100%;
  position: relative;
}
#main .list-slider .content-wrap ul.list-item-gallery li a figure {
  position: relative;
  aspect-ratio: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  justify-contents: center;
}
#main .list-slider .content-wrap ul.list-item-gallery li a figure img {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
#main .list-slider .content-wrap ul.list-item-gallery li a figure .favorite {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 34px;
  height: 34px;
  padding: 2px 2px;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
  background-image: url(../img/common/icn_favorite.svg);
  background-size: 26px auto;
  background-repeat: no-repeat;
  background-position: 3px 3px;
  background-color: #fff;
}
#main .list-slider .content-wrap ul.list-item-gallery li a figure .favorite::hover {
  opacity: 0.7;
}
#main .list-slider .content-wrap ul.list-item-gallery li a figure .favorite.active {
  background-image: url(../img/common/icn_favorite_on.svg);
  background-size: 26px auto;
  background-repeat: no-repeat;
  background-position: 3px 3px;
}
#main .list-slider .content-wrap ul.list-item-gallery li a .item-name {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  margin: 0 auto;
  padding: 0.5rem;
  color: #fff;
  background-color: #444;
  font-size: 0.8rem;
  font-weight: 700;
  line-height: 1.4;
  border: none;
}
#main .list-slider .content-wrap ul.list-item-gallery li a .current-price {
  padding: 0.5rem;
}
#main .list-slider .content-wrap ul.list-item-gallery li a .current-price .price-c {
  color: #231914;
  font-size: 12px;
}
#main .list-slider .content-wrap ul.list-item-gallery li a .current-price .price-v {
  color: #E50A09;
  font-size: 1rem;
  font-weight: 700;
}
#main .list-slider .content-wrap ul.list-item-gallery li a .current-price .price-u {
  color: #E50A09;
  font-size: 1rem;
  font-weight: 700;
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  padding: 0;
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl dt, #main .list-slider .content-wrap ul.list-item-gallery li a dl dd {
  background-color: #E8E7E7;
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl dt {
  width: 90px;
  margin: 0 1px 0 0;
  padding: 0.5rem 0.5rem 0.5rem 2.5rem;
  font-size: 0.8rem;
  background: url("../img/common/icn_hammer_list.png") no-repeat;
  background-size: 22px 24px;
  background-color: #e7e7e7;
  background-position: 10px 50%;
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery li a dl dt {
    width: 60px;
    padding: 3px;
    font-size: 0.7rem;
    background-size: 18px 20px;
    background-position: 7px 10px;
  }
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery li a dl dt .bid-l {
    padding: 8px 0 0 26px;
  }
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl dt .bid-v {
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery li a dl dt .bid-v {
    width: 100%;
    padding: 10px 0 0;
    font-size: 0.8rem;
    text-align: center;
  }
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl dd {
  width: calc(100% - 91px);
  margin: 0;
  padding: 0.5rem 0.5rem 0.5rem 2.7rem;
  font-size: 0.8rem;
  background: url("../img/common/icn_clock_list.png") no-repeat;
  background-size: 24px 24px;
  background-color: #e7e7e7;
  background-position: 10px 50%;
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery li a dl dd {
    width: calc(100% - 61px);
    padding: 3px;
    font-size: 0.7rem;
    background-image: none;
  }
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery li a dl dd .end-l {
    padding: 3px;
  }
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl dd .end-l span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl dd .end-l span.value {
  display: inline-block;
  font-size: 0.8rem;
  font-weight: 700;
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl dd .end-l span.limit {
  color: #ff0000;
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery li a dl dd .end-v {
    width: 100%;
    padding: 3px;
  }
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl dd .end-v span.label {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
#main .list-slider .content-wrap ul.list-item-gallery li a dl dd .end-v span.value {
  display: inline-block;
  font-size: 0.8rem;
  font-weight: 700;
}
#main .list-slider .content-wrap ul.list-item-gallery button.slick-arrow {
  top: calc(50% - 20px);
  width: 40px;
  height: 40px;
  border-radius: 50px;
  background-color: rgba(0, 0, 0, 0.3);
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery button.slick-arrow {
    top: calc(50% - 36px);
    width: 34px;
    height: 34px;
  }
}
#main .list-slider .content-wrap ul.list-item-gallery button.slick-prev.slick-arrow {
  left: 1px;
}
#main .list-slider .content-wrap ul.list-item-gallery button.slick-prev.slick-arrow:before {
  content: "";
  width: 8px;
  height: 8px;
  border: 0;
  border-top: solid 2px #fff;
  border-right: solid 2px #fff;
  position: absolute;
  top: calc(50% - 1px);
  right: calc(50% - 7px);
  -webkit-transform: rotate(-135deg);
          transform: rotate(-135deg);
}
#main .list-slider .content-wrap ul.list-item-gallery button.slick-next.slick-arrow {
  right: 1px;
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery button.slick-next.slick-arrow {
    right: 0;
  }
}
#main .list-slider .content-wrap ul.list-item-gallery button.slick-next.slick-arrow:before {
  content: "";
  width: 8px;
  height: 8px;
  border: 0;
  border-top: solid 2px #fff;
  border-right: solid 2px #fff;
  position: absolute;
  top: calc(50% - 1px);
  left: calc(50% - 7px);
}
@media screen and (max-width: 767px) {
  #main .list-slider .content-wrap ul.list-item-gallery button.slick-next.slick-arrow:before {
    left: calc(50% - 11px);
  }
}
#main .list-slider .content-wrap ul.list-item-gallery .slick-dots {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  margin: 0 auto;
}
#main .list-slider .content-wrap ul.list-item-gallery .slick-dots li {
  width: 20px;
  margin: 0 5px;
  background-color: transparent;
  border: none;
}
#main .list-slider .content-wrap ul.list-item-gallery .slick-dots li.slick-active button:before {
  color: #e60012;
}
#main .list-slider .content-wrap ul.list-item-gallery .slick-dots li button:before {
  font-size: 10px;
}

.pagination {
  width: 100%;
  margin: 2rem 0 3rem;
  padding: 0 1rem;
  text-align: center;
}
.pagination .number {
  margin: 1rem 0;
  text-align: center;
  font-size: 14px;
}
.pagination ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 5px;
}
.pagination ul li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 52px;
  height: 52px;
  font-size: 14px;
  border: 1px solid #ececec;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  .pagination ul li {
    width: 48px;
    height: 48px;
  }
}
.pagination ul li.this {
  color: #fff;
  background-color: #222;
  border: 1px solid #222;
}
.pagination ul li.prev:after {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  border-top: 3px solid #999;
  border-left: 3px solid #999;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  position: absolute;
  left: calc(50% - 2px);
  top: calc(50% - 3px);
}
.pagination ul li.next:after {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  border-top: 3px solid #999;
  border-left: 3px solid #999;
  -webkit-transform: rotate(135deg);
  transform: rotate(135deg);
  position: absolute;
  right: calc(50% - 2px);
  top: calc(50% - 3px);
}
.pagination ul li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 100%;
  padding: 0;
  font-size: 14px;
}
.pagination ul li a:hover {
  background-color: #f5f5f5;
}

/* --------------------------- */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 40px 20px;
  overflow: auto;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  z-index: 100;
}
.modal-container:before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
.modal-container.active {
  opacity: 1;
  visibility: visible;
}
.modal-container .modal-body {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  max-width: calc(100% - 2rem);
  width: 900px;
  margin: 0 auto;
}
.modal-container .modal-body .modal-close {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  top: -24px;
  right: -24px;
  width: 60px;
  height: 60px;
  font-size: 22px;
  color: #fff;
  line-height: 1;
  background-color: #929392;
  border-radius: 30px;
  cursor: pointer;
  z-index: 120;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-close {
    width: 50px;
    height: 50px;
  }
}
.modal-container .modal-body .modal-content {
  position: relative;
  padding: 3rem;
  background-color: #fff;
  border-radius: 10px;
  z-index: 110;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content {
    padding: 6vw;
    font-size: 3.5vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap {
  width: 100%;
  height: auto;
  max-height: 400px;
  overflow: auto;
  border: 1px solid #ccc;
  scrollbar-width: thin;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap {
    max-height: 78vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap {
  width: 100%;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap {
    margin: 0;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  padding: 0.5rem;
  background-color: #d7d7d7;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head {
    padding: 3vw 0.5rem;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .ttl-auction {
  margin: 0 0 0.5rem;
  padding: 0;
  font-size: 1rem;
  font-weight: bold;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .ttl-auction {
    margin: 0 0 1vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  margin: 0;
  padding: 0;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule {
    margin: 0;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  width: 100%;
  margin: 0 auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row {
    width: 100%;
    margin: 0;
    padding: 0;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .country, .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .cont {
  padding: 3px 0;
  font-size: 13px;
  line-height: 1.1;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .country, .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .cont {
    font-size: 3vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .country {
  width: 120px;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .country {
    width: 14vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .country .wic {
  position: relative;
  display: inline-block;
  padding: 0;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .cont {
  width: calc(100% - 120px);
  white-space: wrap;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .cont {
    width: calc(100% - 14vw);
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .cont .date {
  display: inline-block;
  margin: 0 0.5rem;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .cont .time {
  display: inline-block;
  margin: 0 0.5rem;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .matching-head .schedule .sch-row .cont .symbol {
  display: inline-block;
  margin: 0 0.5rem;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table {
  width: 100%;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table {
    border-bottom: none;
  }
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table thead {
    display: none;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table thead tr th {
  min-width: 78px;
  padding: 9px 10px;
  color: #000;
  font-size: 10px;
  line-height: 1.2;
  text-align: center;
  vertical-align: middle;
  background-color: #eee;
  border-bottom: 1px solid #aeaeae;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table thead tr th {
    font-size: 2.4vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table thead tr th.t-left {
  text-align: left;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table thead tr th.num {
  white-space: nowrap;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody {
  width: 100%;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr {
  display: table-row;
  border-bottom: 1px solid #aeaeae;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr {
    display: block;
    width: 100%;
    margin: 0;
    padding: 2vw 0.5rem;
    border-top: 1px solid #747474;
    border-bottom: none;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr:last-child {
  border-bottom: 1px solid #747474;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr:last-child {
    border-bottom: 1px solid #747474;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td {
  padding: 11px 10px;
  font-size: 13px;
  text-align: center;
  line-height: 1.2;
  vertical-align: middle;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td {
    width: 100%;
    position: relative;
    display: block;
    padding: 5px 0;
    font-size: 3vw;
    text-align: right;
    border-bottom: 1px solid #e4e4e4;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td:before {
  position: absolute;
  left: 0;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.maker {
  max-width: 140px;
  text-align: left;
  -webkit-box-flex: 2;
      -ms-flex-positive: 2;
          flex-grow: 2;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.maker {
    max-width: 100%;
    padding: 8px 0 8px 5em;
    text-align: right;
  }
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.maker:before {
    content: "";
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.itemname {
  padding: 11px 10px 11px 10px;
  text-align: left;
  -webkit-box-flex: 2;
      -ms-flex-positive: 2;
          flex-grow: 2;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.itemname {
    padding: 8px 0 8px 5em;
    text-align: right;
  }
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.itemname:before {
    content: "";
  }
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.sim:before {
    content: "";
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.capacity {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.capacity:before {
    content: "";
  }
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.color:before {
    content: "";
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.grade {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.grade:before {
    content: "";
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.quantity {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.quantity:before {
    content: "";
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.quantity-min {
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.quantity-min:before {
    content: "";
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.price-min {
  font-family: "sans-serif", "system-ui";
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.price-min:before {
    content: "";
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.check {
  position: relative;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.check:after {
  content: "";
  position: absolute;
  top: 15%;
  left: 0;
  width: 1px;
  height: 70%;
  background: #e3e3e3;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.check:after {
    display: none;
  }
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.check {
    border-bottom: none;
  }
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.check:before {
    content: "";
    top: 10px;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.check button.fav-mark {
  background: transparent;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.check button.fav-mark .fav-pct {
  width: 24px;
  height: 24px;
  background-image: url(../img/common/icn_favorite_gr.svg);
  background-size: 24px 24px;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.check button.fav-mark .fav-pct:hover {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td.check button.fav-mark .fav-pct.added {
  background-image: url(../img/common/icn_favorite.svg);
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td:last-child {
  border-bottom: none;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap table tbody tr td a {
  color: #0084c1;
  font-weight: 500;
  line-height: 1.2;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .total-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 0.5rem;
  border-bottom: 2px solid #747474;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .total-price .ttl {
  width: 60%;
  font-size: 13px;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .total-price .ttl {
    font-size: 3vw;
    line-height: 1.2;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-wrap .total-price .pri {
  width: calc(40% - 1vw);
  padding: 0 0 0 1vw;
  line-height: 1.2;
  text-align: right;
  font-size: 13px;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount .transaction {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 0.8rem 0.5rem;
  background-color: #faf1f2;
  border-bottom: 2px solid #747474;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount .transaction {
    padding: 1.1rem 0.5rem;
    border-bottom: 2px solid #747474;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount .transaction .ttl {
  width: 60%;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount .transaction .ttl {
    font-size: 3vw;
    line-height: 1.2;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount .transaction .pri {
  width: calc(40% - 1vw);
  padding: 0 0 0 1vw;
  line-height: 1.2;
  text-align: right;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap {
  padding: 1rem;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap {
    padding: 0.5rem;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap .auction-ttl {
  margin: 0 0 0.3rem;
  padding: 0;
  font-size: 14.4px;
  font-weight: bold;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap .auction-ttl {
    font-size: 3vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list {
  border-top: 1px solid #ccc;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li {
  padding: 0.3rem 0;
  border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li {
    padding: 0.3rem 0 0.5rem;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li p {
  width: 100%;
  font-size: 14.4px;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li p {
    font-size: 3vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li p.bid-item-name {
  border: none;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li p.bid-item-name span {
  margin: 0 1rem 0 0;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li p span {
  display: inline-block;
  margin: 0 0.5rem 0 0;
  font-family: "sans-serif", "system-ui";
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li p span.label {
  position: relative;
  margin: 0 10px 0 0;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li p span.label:after {
  position: absolute;
  right: -7px;
  content: ":";
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li div.criterion {
  font-size: 14.4px;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li div.criterion {
    font-size: 3vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li div.criterion dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li div.criterion dl {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li div.criterion dl dt {
  min-width: 4rem;
  margin-right: 0.7rem;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li div.criterion dl.success dt {
  color: #008ece;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li div.criterion dl.failure dt {
  color: #ff0000;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap ul.bid-list li div.criterion dl.failure dd {
  color: #ff0000;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap .auction-total {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 0.3rem 0 0;
  font-size: 14.4px;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap .auction-total {
    font-size: 3vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap .auction-total .label {
  margin: 0 1rem 0 0;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .auction-line-wrap .auction-total .price {
  font-family: "sans-serif", "system-ui";
}
.modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 1.5rem 0 0;
  padding: 1.3rem 1rem;
  font-weight: 600;
  border-top: 2px solid #ccc;
  border-bottom: 2px solid #ccc;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount {
    padding: 1rem 0.5rem;
    font-size: 3.5vw;
  }
}
.modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount p {
  font-weight: 600;
}
.modal-container .modal-body .modal-content .matching-dir-wrap .total-bid-amount p.price {
  font-family: "sans-serif", "system-ui";
}
.modal-container .modal-body .modal-content .error-contents-wrap {
  margin: 0 0 1rem;
}
.modal-container .modal-body .modal-content .error-contents-wrap .error-item {
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
}
.modal-container .modal-body .modal-content .error-contents-wrap .error-item p.item-head span {
  margin: 0 1rem 0 0;
}
.modal-container .modal-body .modal-content .error-contents-wrap .error-item p span {
  display: inline-block;
  margin: 0 0.5rem 0 0;
}
.modal-container .modal-body .modal-content .error-contents-wrap .error-item p span.label {
  position: relative;
  margin: 0 10px 0 0;
}
.modal-container .modal-body .modal-content .error-contents-wrap .error-item p span.label:after {
  position: absolute;
  right: -7px;
  content: ":";
}
.modal-container .modal-body .modal-content .error-contents-wrap .error-message {
  padding: 0.5rem 1rem;
}
.modal-container .modal-body .modal-content .error-contents-wrap .error-message .error {
  margin: 0.5rem 0;
  color: #ff0000;
  text-align: center;
  line-height: 1.4;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .error-contents-wrap .error-message .error {
    margin: 0;
    font-size: 3vw;
  }
}
.modal-container .modal-body .modal-content .rule {
  width: 820px;
  max-width: 100%;
  margin: 20px auto;
}
.modal-container .modal-body .modal-content .rule p {
  padding: 0 0.3rem 0.2rem;
  font-size: 0.9rem;
  font-weight: nomal;
  text-align: center;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .rule p {
    font-size: 3.5vw;
  }
}
.modal-container .modal-body .modal-content .rule embed {
  width: 100%;
  height: 100px;
  border: 1px solid #ccc;
  scrollbar-width: thin;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.modal-container .modal-body .modal-content .rule .rule-check {
  margin: 0.5rem 0 0;
  text-align: center;
}
.modal-container .modal-body .modal-content .note-bid {
  width: 100%;
  padding: 0 1rem;
  font-size: 12px;
  text-align: center;
}
.modal-container .modal-body .modal-content .button-bid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  height: auto;
}
.modal-container .modal-body .modal-content .button-bid button {
  width: 280px;
  max-width: calc(100% - 2rem);
  height: 56px;
  margin: 0.5rem auto 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  background-color: #e60012;
  border-radius: 4px;
}
@media screen and (max-width: 1080px) {
  .modal-container .modal-body .modal-content .button-bid button {
    width: auto;
    min-width: calc(100% - 2rem);
  }
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .button-bid button {
    width: calc(100% - 2rem);
  }
}
.modal-container .modal-body .modal-content .button-bid button[disabled] {
  background-color: #814340;
  color: #918281;
}
.modal-container .modal-body .modal-content .button-modal-close {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  height: auto;
}
.modal-container .modal-body .modal-content .button-modal-close button {
  width: 280px;
  max-width: calc(100% - 2rem);
  height: 56px;
  margin: 0.5rem auto 0;
  color: #fff;
  font-weight: 500;
  background-color: #444;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  .modal-container .modal-body .modal-content .button-modal-close button {
    width: calc(100% - 2rem);
    font-size: 3.8vw;
  }
}
/*# sourceMappingURL=parts.css.map */
