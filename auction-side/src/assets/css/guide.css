@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *ガイド
 * *********************************************************************** */
#main #beginner {
  padding-bottom: 80px;
}
@media screen and (max-width: 767px) {
  #main #beginner {
    padding: 36px 4vw 40px;
  }
}
#main #beginner .container {
  padding: 3rem 1rem 0;
}
@media screen and (max-width: 767px) {
  #main #beginner .container {
    padding: 0;
  }
}
#main #beginner .container .table-contents {
  width: 1280px;
  max-width: 100%;
  margin: 0 auto 3rem;
  padding: 1.5rem 4rem 2.5rem;
  background-color: #f7f7f7;
  border-radius: 8px;
}
@media screen and (max-width: 767px) {
  #main #beginner .container .table-contents {
    margin: 0 auto 2rem;
    padding: 4vw 6vw 5vw;
  }
}
#main #beginner .container .table-contents h2 {
  margin: 0 0 1.3rem;
  font-size: 18px;
}
@media screen and (max-width: 767px) {
  #main #beginner .container .table-contents h2 {
    margin: 0 0 4vw;
    font-size: 4vw;
  }
}
#main #beginner .container .table-contents .list-wrap {
  padding: 0.5rem 2.3rem;
  background-color: #fff;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main #beginner .container .table-contents .list-wrap {
    padding: 1.5vw 4vw;
  }
}
#main #beginner .container .table-contents .list-wrap ul {
  width: 1180px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
}
#main #beginner .container .table-contents .list-wrap ul li {
  padding: 0;
  border-bottom: 1px solid #eee;
}
@media screen and (max-width: 767px) {
  #main #beginner .container .table-contents .list-wrap ul li {
    margin: 1px 0;
  }
}
#main #beginner .container .table-contents .list-wrap ul li a {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  padding: 0.7rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  #main #beginner .container .table-contents .list-wrap ul li a {
    padding: 2.3vw 4vw;
    font-size: 3.4vw;
  }
}
#main #beginner .container .table-contents .list-wrap ul li a:hover {
  color: #e60012;
}
#main #beginner .container .table-contents .list-wrap ul li a:hover:after {
  border-top: solid 2px #e60012;
  border-right: solid 2px #e60012;
}
#main #beginner .container .table-contents .list-wrap ul li a:after {
  position: absolute;
  top: calc(50% - 3px);
  right: 1rem;
  content: "";
  width: 6px;
  height: 6px;
  border: 0;
  border-top: solid 2px #d6d6d6;
  border-right: solid 2px #d6d6d6;
  transform: rotate(135deg);
}
@media screen and (max-width: 767px) {
  #main #beginner .container .table-contents .list-wrap ul li a:after {
    width: 4px;
    height: 4px;
    right: 5vw;
  }
}
#main #beginner .container .table-contents .list-wrap ul li a .stp {
  display: inline-block;
  margin: 0 3rem 0 0;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #beginner .container .table-contents .list-wrap ul li a .stp {
    margin: 0 6vw 0 0;
    font-size: 3.4vw;
  }
}
#main #beginner .container .table-contents .list-wrap ul li:last-child {
  border-bottom: none;
}
#main #beginner ul.step > li {
  position: relative;
  margin-bottom: 70px;
  border-radius: 12px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.15);
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li {
    margin-bottom: 58px;
  }
}
#main #beginner ul.step > li::after {
  content: "";
  display: block;
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  border-top: 26px solid #e60012;
  border-right: 18px solid transparent;
  border-left: 18px solid transparent;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li::after {
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    border-top: 20px solid #e60012;
    border-right: 14px solid transparent;
    border-left: 14px solid transparent;
  }
}
#main #beginner ul.step > li:last-of-type {
  margin-bottom: 30px;
}
#main #beginner ul.step > li:last-of-type::before, #main #beginner ul.step > li:last-of-type::after {
  display: none;
}
#main #beginner ul.step > li h3 {
  position: relative;
  display: flex;
  align-items: center;
  height: 70px;
  margin: 0;
  padding: 0 40px;
  color: #fff;
  font-weight: 700;
  font-size: 22px;
  text-align: left;
  background-color: #e60012;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li h3 {
    height: auto;
    min-height: 55px;
    padding: 2.7vw 6vw;
    font-size: 4vw;
  }
}
#main #beginner ul.step > li h3 em {
  display: block;
  width: 200px;
  font-weight: 700;
  text-align: center;
  flex-shrink: 0;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li h3 em {
    width: 20vw;
    text-align: left;
  }
}
#main #beginner ul.step > li .conts {
  padding: 60px 40px 60px;
  background-color: #f7f7f7;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts {
    padding: 4vw 6vw 8vw;
  }
}
#main #beginner ul.step > li .conts .outline {
  display: flex;
  font-size: 18px;
  justify-content: space-between;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts .outline {
    display: block;
    font-size: 1rem;
  }
}
#main #beginner ul.step > li .conts .outline .out-img {
  display: flex;
  width: 200px;
  flex-shrink: 0;
  margin: 0;
  justify-content: center;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts .outline .out-img {
    width: 100%;
    margin: 4vw 0 4vw;
    text-align: center;
  }
}
#main #beginner ul.step > li .conts .outline .out-img img {
  max-width: 100px;
  max-height: max-content;
  height: auto;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts .outline .out-img img {
    width: 20vw;
  }
}
#main #beginner ul.step > li .conts .outline .out-txt {
  display: flex;
  flex-direction: column;
  width: calc(100% - 200px);
  padding: 0 40px 0 0;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts .outline .out-txt {
    width: 100%;
    padding: 0;
  }
}
#main #beginner ul.step > li .conts .outline .out-txt .flow {
  margin: 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts .outline .out-txt .flow {
    margin: 0 0 4vw;
  }
}
#main #beginner ul.step > li .conts .outline .out-txt .flow li {
  display: flex;
  flex-direction: row;
  margin: 0 0 0.3rem;
  font-size: 1.1rem;
  line-height: 1.6;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts .outline .out-txt .flow li {
    margin: 0 0 2vw;
    font-size: 3.5vw;
    line-height: 1.4;
  }
}
#main #beginner ul.step > li .conts .outline .out-txt .flow li .sym {
  display: block;
  margin: 0 1rem 0 0;
  font-weight: 700;
  font-size: 1.4rem;
  line-height: 1.15;
  white-space: nowrap;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts .outline .out-txt .flow li .sym {
    font-size: 4.3vw;
    line-height: 1.2;
  }
}
#main #beginner ul.step > li .conts .outline .out-txt .ann {
  padding: 0 0 0 3rem;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts .outline .out-txt .ann {
    padding: 0 0 0 2vw;
  }
}
#main #beginner ul.step > li .conts .outline .out-txt .ann li {
  margin: 0 0 0.2rem;
  padding: 0 0 0 1rem;
  text-indent: -0.6rem;
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li .conts .outline .out-txt .ann li {
    margin: 0 0 2vw;
    font-size: 3vw;
    padding: 0 0 0 1vw;
    text-indent: -2vw;
    line-height: 1.4;
  }
}
#main #beginner ul.step > li.step-1 .btn-signup {
  width: 100%;
  max-width: 370px;
  margin: 30px auto 0;
}
#main #beginner ul.step > li.step-1 .btn-signup a span {
  font-weight: 700;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li.step-1 .btn-signup a span {
    font-size: 12px;
  }
}
#main #beginner ul.step > li.step-1 .judge {
  background-color: #fff;
  margin-top: 40px;
  padding: 30px 10px 30px 40px;
}
@media screen and (max-width: 767px) {
  #main #beginner ul.step > li.step-1 .judge {
    padding: 20px;
  }
}

#main #member-registration {
  width: 100%;
  padding: 80px 0 80px;
  background-color: #FBF5F5;
}
@media screen and (max-width: 767px) {
  #main #member-registration {
    padding: 60px 4vw;
  }
}
#main #member-registration p.desc-an {
  margin: 20px 0 40px;
  line-height: 1.6;
}
@media screen and (max-width: 767px) {
  #main #member-registration p.desc-an {
    margin: 4vw 0 8vw;
  }
}
#main #member-registration p.desc-an span {
  display: block;
  margin: 0 auto;
  font-weight: 500;
  text-align: center;
}
#main #member-registration ul.entry {
  width: 700px;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #main #member-registration ul.entry {
    width: 100%;
  }
}
#main #member-registration ul.entry li {
  position: relative;
  margin: 0 0 50px;
  padding: 1.5rem 1.2rem 1.8rem;
  border: 3px solid #bb0000;
  background-color: #fff;
  border-radius: 12px;
}
@media screen and (max-width: 767px) {
  #main #member-registration ul.entry li {
    margin: 0 0 40px;
    padding: 6vw 4vw 8vw;
    border: 2px solid #bb0000;
  }
}
#main #member-registration ul.entry li::after {
  content: "";
  display: block;
  position: absolute;
  bottom: -34px;
  left: 50%;
  transform: translateX(-50%);
  border-top: 16px solid #bb0000;
  border-right: 12px solid transparent;
  border-left: 12px solid transparent;
}
@media screen and (max-width: 767px) {
  #main #member-registration ul.entry li::after {
    bottom: -29px;
    left: 50%;
    transform: translateX(-50%);
    border-top: 16px solid #bb0000;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
  }
}
#main #member-registration ul.entry li:last-of-type {
  margin-bottom: 30px;
}
#main #member-registration ul.entry li:last-of-type::before, #main #member-registration ul.entry li:last-of-type::after {
  display: none;
}
#main #member-registration ul.entry li .ttl-flow .step {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  margin: 0 0 0.5rem;
  color: #bb0000;
  font-size: 24px;
}
@media screen and (max-width: 767px) {
  #main #member-registration ul.entry li .ttl-flow .step {
    flex-direction: column;
    font-size: 4.2vw;
  }
}
#main #member-registration ul.entry li .ttl-flow .step .cha-h {
  width: 240px;
  padding: 0;
  font-weight: 600;
  text-align: right;
}
@media screen and (max-width: 767px) {
  #main #member-registration ul.entry li .ttl-flow .step .cha-h {
    width: 100%;
    text-align: center;
  }
}
#main #member-registration ul.entry li .ttl-flow .step .cha-h .num {
  display: inline-block;
  padding: 0 0 0 0.3rem;
  font-size: 36px;
  font-weight: 600;
  font-family: Poppins;
  line-height: 1;
}
@media screen and (max-width: 767px) {
  #main #member-registration ul.entry li .ttl-flow .step .cha-h .num {
    padding: 0 0 0 1vw;
    font-size: 7vw;
    font-weight: 500;
  }
}
#main #member-registration ul.entry li .ttl-flow .step .ttl-txt {
  width: calc(100% - 240px);
  padding: 0 0 0 1.8rem;
  font-weight: 600;
  font-size: 24px;
}
@media screen and (max-width: 767px) {
  #main #member-registration ul.entry li .ttl-flow .step .ttl-txt {
    width: 100%;
    padding: 0;
    text-align: center;
    font-size: 4.8vw;
  }
}
#main #member-registration ul.entry li .desc-flow {
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #member-registration ul.entry li .desc-flow {
    margin: 4vw 0 0;
  }
}
/*# sourceMappingURL=guide.css.map */