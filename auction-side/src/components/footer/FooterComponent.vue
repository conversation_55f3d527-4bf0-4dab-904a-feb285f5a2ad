<script setup>
import { useLocale } from 'vuetify'
import { PATH_NAME } from '../../defined/const'
import { useAuthStore } from '../../stores/auth'
import setupScrollLogic from './setupScrollLogic'

const auth = useAuthStore()
const { t: translate, current } = useLocale()

const openCookieSetting = () => {
  const otSDK = document.getElementById('ot-sdk-btn')
  otSDK.click()
}

const onClickTopBtn = (event) => {
  event.preventDefault()
  $('body,html').animate(
    {
      scrollTop: 0
    },
    500
  ) // 0.5秒かけてトップへ移動
  return false
}

// 「トップへ戻る」ボタンをフッター手前でストップ
setupScrollLogic()
</script>

<template>
  <footer>
    <!-- ページトップボタンここから -->
    <div id="page_top"><a @click="onClickTopBtn"></a></div>
    <!-- ページトップボタンここまで -->
    <nav>
      <!-- PC fNav -->
      <div class="fNav_pc only_pc container">
        <div class="fNav-1">
          <div class="pct-logo">
            <RouterLink :to="PATH_NAME.TOP">
              <img src="@/assets/img/common/logo_worldmobile.svg" alt="WORLD MOBILE" />
            </RouterLink>
          </div>
        </div>
        <ul class="fNav-2">
          <li>
            <p>{{ translate('guidance.aboutMembership') }}</p>
            <ul>
              <template v-if="!auth.isAuthenticated">
                <li class="work-break">
                  <RouterLink :to="PATH_NAME.ENTRY_INFO_REGIST">
                    {{ translate('guidance.register') }}
                  </RouterLink>
                </li>
                <li>
                  <RouterLink :to="PATH_NAME.LOGIN">
                    {{ translate('guidance.login') }}
                  </RouterLink>
                </li>
              </template>
              <template v-else>
                <li>
                  <RouterLink :to="PATH_NAME.FAVORITES">
                    {{ translate('user.favorite') }}
                  </RouterLink>
                </li>
                <li>
                  <RouterLink :to="PATH_NAME.BIDS">
                    {{ translate('user.bidOngoing') }}
                  </RouterLink>
                </li>
                <li>
                  <RouterLink :to="PATH_NAME.BID_HISTORY">
                    {{ translate('user.bidHistory') }}
                  </RouterLink>
                </li>
                <li>
                  <RouterLink :to="PATH_NAME.MYPAGE">
                    {{ translate('user.editProfile') }}
                  </RouterLink>
                </li>
              </template>
            </ul>
          </li>
          <li>
            <p>{{ translate('guidance.firstTime') }}</p>
            <ul>
              <li class="work-break">
                <RouterLink :to="PATH_NAME.GUIDE">
                  {{ translate('guidance.guide') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li>
            <p>{{ translate('guidance.information') }}</p>
            <ul>
              <li class="work-break">
                <RouterLink :to="PATH_NAME.CONTACT">
                  {{ translate('guidance.contact') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li>
            <p>{{ translate('guidance.companyInfo') }}</p>
            <ul>
              <li class="work-break">
                <RouterLink :to="PATH_NAME.PROFILE">
                  {{ translate('guidance.companyOverview') }}
                </RouterLink>
              </li>
              <li class="work-break">
                <RouterLink :to="PATH_NAME.TERMS">
                  {{ translate('guidance.terms') }}
                </RouterLink>
              </li>
              <li class="work-break">
                <RouterLink :to="PATH_NAME.ORDER_CONTRACT">
                  {{ translate('guidance.toshuho') }}
                </RouterLink>
              </li>
              <li class="work-break">
                <RouterLink :to="PATH_NAME.PRIVACY">
                  {{ translate('guidance.privacy') }}
                </RouterLink>
              </li>
              <li>
                <a
                  ><button class="cursor-pointer cookie-btn" @click="openCookieSetting">
                    {{ translate('guidance.cookieSetting') }}
                  </button></a
                >
              </li>
            </ul>
          </li>
        </ul>
      </div>

      <!-- SP fNav -->
      <div class="fNav_sp only_sp">
        <div class="fNav-1">
          <div class="pct-logo">
            <a href="#"
              ><img src="@/assets/img/common/logo_worldmobile.svg" alt="WORLD MOBILE"
            /></a>
          </div>
        </div>
        <ul>
          <li class="nav-ttl"><a href="/">TOP</a></li>
          <li class="nav-black">
            <p>{{ translate('guidance.aboutMembership') }}</p>
            <ul>
              <template v-if="!auth.isAuthenticated">
                <li class="work-break">
                  <RouterLink :to="PATH_NAME.ENTRY_INFO_REGIST">
                    {{ translate('guidance.register') }}
                  </RouterLink>
                </li>
                <li>
                  <RouterLink :to="PATH_NAME.LOGIN">
                    {{ translate('guidance.login') }}
                  </RouterLink>
                </li>
              </template>
              <template v-else>
                <li>
                  <RouterLink :to="PATH_NAME.FAVORITES">
                    {{ translate('user.favorite') }}
                  </RouterLink>
                </li>
                <li>
                  <RouterLink :to="PATH_NAME.BIDS">
                    {{ translate('user.bidOngoing') }}
                  </RouterLink>
                </li>
                <li>
                  <RouterLink :to="PATH_NAME.BID_HISTORY">
                    {{ translate('user.bidHistory') }}
                  </RouterLink>
                </li>
                <li>
                  <RouterLink :to="PATH_NAME.MYPAGE">
                    {{ translate('user.editProfile') }}
                  </RouterLink>
                </li>
              </template>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('guidance.firstTime') }}</p>
            <ul>
              <li>
                <RouterLink :to="PATH_NAME.GUIDE">
                  {{ translate('guidance.guide') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('guidance.information') }}</p>
            <ul>
              <li>
                <RouterLink :to="PATH_NAME.CONTACT">
                  {{ translate('guidance.contact') }}
                </RouterLink>
              </li>
            </ul>
          </li>
          <li class="nav-black">
            <p>{{ translate('guidance.companyInfo') }}</p>
            <ul>
              <li>
                <RouterLink :to="PATH_NAME.PROFILE">
                  {{ translate('guidance.companyOverview') }}
                </RouterLink>
              </li>
              <li>
                <RouterLink :to="PATH_NAME.TERMS">
                  {{ translate('guidance.terms') }}
                </RouterLink>
              </li>
              <li class="work-break">
                <RouterLink :to="PATH_NAME.ORDER_CONTRACT">
                  {{ translate('guidance.toshuho') }}
                </RouterLink>
              </li>
              <li class="work-break">
                <RouterLink :to="PATH_NAME.PRIVACY">
                  {{ translate('guidance.privacy') }}
                </RouterLink>
              </li>
              <li>
                <a
                  ><button class="cursor-pointer cookie-btn" @click="openCookieSetting">
                    {{ translate('guidance.cookieSetting') }}
                  </button></a
                >
              </li>
            </ul>
          </li>
        </ul>
      </div>
      <!-- OneTrust Cookie 設定ボタンの始点 -->
      <button hidden id="ot-sdk-btn" class="ot-sdk-show-settings"></button>
      <!-- OneTrust Cookie 設定ボタンの終点 -->
    </nav>
    <div class="copyright">
      <p>
        <small>{{ translate('copyright') }}</small>
      </p>
    </div>
  </footer>
</template>
<style scoped lang="css">
*,
::before,
::after {
  box-sizing: content-box !important;
}
.work-break {
  word-break: break-word;
}
.fNav-1 {
  width: initial !important;
}
.cookie-btn {
  font-size: 13px;
  line-height: 1.2;
  letter-spacing: 1px;
  text-decoration: none;
}
</style>
