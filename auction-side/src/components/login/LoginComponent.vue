<script setup lang="ts">
import { computed, ref, type Ref, type ComputedRef } from 'vue'
import {
  RouterLink,
  useRoute,
  useRouter,
  type Router,
  type RouteLocationNormalizedLoaded
} from 'vue-router'
import { useLocale } from 'vuetify'
import { aesDecrypt, aesEncrypt } from '../../composables/common.js'
// import useApi from '../../composables/useApi.js'
import { API_PATH, PATH_NAME, PATTERN, loginNextStep } from '../../defined/const.js'
import { useAuthStore } from '../../stores/auth.js'
import { useLanguageStore } from '../../stores/language.js'
import ChangePassword from './ChangePassword.vue'

// Type definitions
type ValidationCheck = {
  pass: boolean
  errMsg: string
}

type ValidationResult = {
  email: ValidationCheck
  password: ValidationCheck
}

type LoginData = {
  user_id: string
  password: string
}

// Store instances
const auth = useAuthStore()
const languageStore = useLanguageStore()

// Router and route
const route: RouteLocationNormalizedLoaded = useRoute()
const router: Router = useRouter()

// Reactive state
const openChangePasswordModal: Ref<boolean> = ref(false)
const tempToken: Ref<string | null> = ref(null)
const email: Ref<string> = ref('')
const password: Ref<string> = ref('')
const saveLoginInfoFlag: Ref<boolean> = ref(false)
const errorMsg: Ref<string> = ref('')

// Locale
const { t: translate } = useLocale()

// Constants
const errClass: string = 'ime-dis err'
const { VITE_LOCALSTORAGE_LOGIN_INFO_LABEL } = import.meta.env

// Initialize saved login data
if (localStorage[VITE_LOCALSTORAGE_LOGIN_INFO_LABEL]) {
  try {
    const loginData: LoginData = aesDecrypt(localStorage[VITE_LOCALSTORAGE_LOGIN_INFO_LABEL])

    if (loginData?.user_id && loginData.password) {
      email.value = loginData.user_id
      password.value = loginData.password
      saveLoginInfoFlag.value = true
    }
  } catch (error) {
    console.warn('Failed to decrypt saved login data:', error)
  }
}

// Validation computed properties
const checkInput: ComputedRef<ValidationResult> = computed(() => {
  const check: ValidationResult = {
    email: { pass: false, errMsg: 'メールアドレスを確認してください。' },
    password: { pass: false, errMsg: 'パスワードを確認してください。' }
  }

  if (PATTERN.EMAIL.test(email.value)) {
    check.email.pass = true
    check.email.errMsg = ''
  }

  if (password.value !== '') {
    check.password.pass = true
    check.password.errMsg = ''
  }

  return check
})

const isLoginDisabled: ComputedRef<boolean> = computed(() => {
  return !(checkInput.value.email.pass && checkInput.value.password.pass)
})

// Simplified login function (no API call, just validation and redirect)
const sendRequest = async (): Promise<void> => {
  try {
    // Clear any previous errors
    errorMsg.value = ''
    tempToken.value = null
    openChangePasswordModal.value = false

    // Create login data object
    const loginData: LoginData = {
      user_id: email.value,
      password: password.value
    }

    // Set mock token and user data
    auth.setToken('mock-token-for-development')
    auth.setNickname(email.value.split('@')[0] || 'User')

    // Handle save login info
    if (saveLoginInfoFlag.value) {
      localStorage[import.meta.env.VITE_LOCALSTORAGE_LOGIN_INFO_LABEL] = aesEncrypt(loginData)
    } else {
      localStorage.removeItem(import.meta.env.VITE_LOCALSTORAGE_LOGIN_INFO_LABEL)
    }

    // Redirect to the intended page or top page
    await router.replace(route.redirectedFrom?.path ?? PATH_NAME.TOP)
  } catch (error) {
    console.error('Login error:', error)
    errorMsg.value = 'ログイン処理中にエラーが発生しました。'
  }
}
</script>
<template>
  <section id="login">
    <h1 class="mb0">{{ translate('login.title') }}</h1>
    <div class="container">
      <section id="login-form">
        <ChangePassword v-model="openChangePasswordModal" :token="tempToken" />
        <form id="loginForm">
          <table class="tbl-login">
            <tbody>
              <tr>
                <th>{{ translate('login.email') }}<em class="req">※</em></th>
                <td>
                  <input
                    id="email"
                    type="text"
                    :class="email === '' || checkInput.email.pass ? 'ime-dis' : errClass"
                    required
                    v-model="email"
                  />
                  <p v-show="email !== '' && !checkInput.email.pass" class="err-txt">
                    {{ checkInput.email.errMsg }}
                  </p>
                </td>
              </tr>
              <tr>
                <th>{{ translate('login.password') }}<em class="req">※</em></th>
                <td>
                  <input
                    id="password"
                    type="password"
                    :class="password === '' || checkInput.password.pass ? 'ime-dis' : errClass"
                    :placeholder="translate('login.passwordHint')"
                    required
                    v-model="password"
                  />
                  <p v-show="password !== '' && !checkInput.password.pass" class="err-txt">
                    {{ checkInput.password.errMsg }}
                  </p>
                </td>
              </tr>
              <tr>
                <th class="only_pc">&nbsp;</th>
                <td class="check-idpass">
                  <label>
                    <input type="checkbox" class="checkbox-input" v-model="saveLoginInfoFlag" />
                    <span class="checkbox-parts">{{ translate('login.saveLoginInfo') }}</span>
                  </label>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="forget-pass">
            <RouterLink :to="PATH_NAME.REMINDER">{{
              translate('login.forgetPassword')
            }}</RouterLink>
          </div>
          <div v-if="errorMsg && errorMsg.length > 0" class="id-pass-err">
            <p class="err-txt">
              {{ errorMsg }}
            </p>
          </div>
          <div class="btn-form">
            <input
              type="button"
              :value="translate('login.confirmButton')"
              @click="sendRequest"
              :disabled="isLoginDisabled"
            />
          </div>
        </form>
        <div class="request">
          <RouterLink :to="PATH_NAME.ENTRY_INFO_REGIST">
            {{ translate('login.entryInfo1') }}
          </RouterLink>
          <p>※{{ translate('login.entryInfo2') }}</p>
        </div>
      </section>
    </div>
  </section>
</template>
