<script setup>
import { onMounted, watch } from 'vue'
import { useLocale } from 'vuetify'
import useNotice from '../../composables/useNotice'
import { PATH_NAME } from '../../defined/const'

defineProps({
  showMore: {
    type: Boolean,
    default: true
  }
})

const { t, current } = useLocale()
const { getNotices, notices } = useNotice()

onMounted(() => {
  getNotices({
    displayCodes: [3],
    limit: null // Get all emergency notices
  })
})

// 言語変更時にデータを再取得する
watch(
  () => current.value,
  () => {
    getNotices({
      displayCodes: [3], // 緊急お知らせのみ表示
      limit: null
    })
  }
)
</script>
<template>
  <section id="news-headline">
    <div class="container">
      <div
        v-for="(notice, i) in notices"
        :key="notice.title"
        :class="{
          wrap: true,
          'px-0': true,
          'mt-0': i === 0,
          'mt-3': i > 0
        }"
      >
        <div class="label">
          <span>{{ t('notice.informationLabel') }}</span>
        </div>
        <dl>
          <dt>{{ notice.create_date }}</dt>
          <dd>
            <router-link :to="`${PATH_NAME.NOTICE_LIST_IMPORTANT}/${notice.notice_no}`">
              <span>{{ notice.title }}</span>
            </router-link>
          </dd>
        </dl>
      </div>
    </div>
  </section>
</template>
<style scoped>
#news-headline .wrap {
  margin-top: 10px;
}
/* Small devices (landscape phones, less than 768px) */
@media screen and (max-width: 767.98px) {
  #news-headline .container {
    padding-left: 16px;
    padding-right: 16px;
  }
}
</style>
