///// viewport処理
var isSp = window.matchMedia("(max-width: 767px)");
// ページ読み込み時に実行
$(window).on("load", function () {
  handleViewport(isSp);
});
// ウィンドウリサイズ時に実行
$(window).on("resize", function () {
  handleViewport(isSp);
});
// ビューポート処理を関数化
function handleViewport(isSp) {
  if (isSp.matches) {
    $("meta[name='viewport']").attr(
      "content",
      "width=device-width,initial-scale=1"
    );
  }
}

///// picture要素 IE対策
document.createElement("picture");

/////[ハンバーガーメニュー]
$(function () {
  $("header p.btnMenu").on("click", function () {
    console.log("btnMenu clicked");
    $("header .gNav").slideToggle();
    $("header p.btnMenu").toggleClass("close");
  });
});

/////[ヘッダー]条件検索ボタン
$(function () {
  // ヘッダー条件検索開閉
  $(".nav-filter a").on("click", function () {
    var filterPanel = $(".filter-panel");

    if (!filterPanel.hasClass("is-active")) {
      filterPanel.addClass("is-active");
    } else {
      filterPanel.removeClass("is-active");
    }
  });

  $(".close-filter span").on("click", function () {
    $(".filter-panel").removeClass("is-active");
  });
  // .nav-btn-searchのクリック時の制御
  //PC条件検索パネルが開いている時はSP検索ボタンを押下してもSP検索パネルが開かないようにする
  $(function () {
    // ヘッダー条件検索開閉
    $(".nav-btn-search").on("click", function () {
      var filterPanelSp = $(".filter-panel-sp");

      // slideToggleを使用してスライドイン/スライドアウトを切り替える
      filterPanelSp.slideToggle();
    });

    $(".close-panel span").on("click", function () {
      $(".filter-panel-sp").slideUp();
    });
  });
});

/////SPナビ 入れ子の開閉
$(function () {
  $("header .gNav nav ul.only_sp li p").click(function () {
    $(this).next("ul").slideToggle();
    $(this).toggleClass("close");
  });
});
$(function () {
  $("footer nav .fNav_sp > ul > li > p").click(function () {
    $(this).next("ul,dl").slideToggle();
    $(this).toggleClass("close");
  });
});

/////スムーススクロール
$(document).ready(function () {
  var urlHash = location.hash;
  if (urlHash) {
    $("body,html").stop().scrollTop(0);
    setTimeout(function () {
      scrollToAnker(urlHash);
    }, 100);
  }
  $('a[href^="#"]').click(function () {
    var href = $(this).attr("href");
    var hash = href == "#" || href == "" ? "html" : href;
    scrollToAnker(hash);
    return false;
  });

  function scrollToAnker(hash) {
    var target = $(hash);
    var position = target.offset().top;
    $("body,html").stop().animate({ scrollTop: position }, 500);
  }
});

/////[ヘッダー・フッター]
// ログイン後
function headerin() {
  $.ajax({
    url: "/inc/header_in.html",
    cache: false,
    async: false,
    dataType: "html",
    success: function (html) {
      document.write(html);
    },
  });
}
// ログイン前
function headerout() {
  $.ajax({
    url: "/inc/header_out.html",
    cache: false,
    async: false,
    dataType: "html",
    success: function (html) {
      document.write(html);
    },
  });
}
function footer() {
  $.ajax({
    url: "/inc/footer.html",
    cache: false,
    async: false,
    dataType: "html",
    success: function (html) {
      document.write(html);
    },
  });
}

/////ユーザーエージェント判別
var ua = navigator.userAgent.toLowerCase();
var ver = navigator.appVersion.toLowerCase();

var isMSIE = ua.indexOf("msie") > -1 && ua.indexOf("opera") == -1;
var isIE6 = isMSIE && ver.indexOf("msie 6.") > -1;
var isIE7 = isMSIE && ver.indexOf("msie 7.") > -1;
var isIE8 = isMSIE && ver.indexOf("msie 8.") > -1;
var isIE9 = isMSIE && ver.indexOf("msie 9.") > -1;
var isIE10 = isMSIE && ver.indexOf("msie 10.") > -1;
var isIE11 = ua.indexOf("trident/7") > -1;
var isIE = isMSIE || isIE11;
var isEdge = ua.indexOf("edge") > -1;
var isChrome = ua.indexOf("chrome") > -1 && ua.indexOf("edge") == -1;
var isFirefox = ua.indexOf("firefox") > -1;
var isSafari = ua.indexOf("safari") > -1 && ua.indexOf("chrome") == -1;
var isOpera = ua.indexOf("opera") > -1;

$(function () {
  if (isOpera) {
    $("body").addClass("js_isOpera");
  } else if (isIE) {
    $("body").addClass("js_isIe");
  } else if (isChrome) {
    $("body").addClass("js_isChrome");
  } else if (isSafari) {
    $("body").addClass("js_isSafari");
  } else if (isEdge) {
    $("body").addClass("js_isEdge");
  } else if (isFirefox) {
    $("body").addClass("js_isFirefox");
  } else {
    return false;
  }
});

var _ua = (function (u) {
  return {
    Tablet:
      (u.indexOf("windows") != -1 &&
        u.indexOf("touch") != -1 &&
        u.indexOf("tablet pc") == -1) ||
      u.indexOf("ipad") != -1 ||
      (u.indexOf("android") != -1 && u.indexOf("mobile") == -1) ||
      (u.indexOf("firefox") != -1 && u.indexOf("tablet") != -1) ||
      u.indexOf("kindle") != -1 ||
      u.indexOf("silk") != -1 ||
      u.indexOf("playbook") != -1,
    Mobile:
      (u.indexOf("windows") != -1 && u.indexOf("phone") != -1) ||
      u.indexOf("iphone") != -1 ||
      u.indexOf("ipod") != -1 ||
      (u.indexOf("android") != -1 && u.indexOf("mobile") != -1) ||
      (u.indexOf("firefox") != -1 && u.indexOf("mobile") != -1) ||
      u.indexOf("blackberry") != -1,
  };
})(window.navigator.userAgent.toLowerCase());

$(function () {
  if (_ua.Mobile) {
    $("body").addClass("js_isMobile");
  } else if (_ua.Tablet) {
    $("body").addClass("js_isTablet");
  } else {
    $("body").addClass("js_isPc");
  }
});

if (navigator.platform.indexOf("Win") != -1) {
  $("body").addClass("js_isWin");
} else {
  $("body").addClass("js_isNotWin");
}

$(function () {
  if (ua.indexOf("iphone") > 0) {
    $("body").addClass("iphone");
  } else if (ua.indexOf("android") > 0 && ua.indexOf("mobile") > 0) {
    $("body").addClass("android");
  } else if (ua.indexOf("ipad") > 0) {
    $("body").addClass("ipad");
  }
});

///// 高さ合わせ
$(function () {
  $(".matchH").matchHeight();
});
/* jquery-match-height 0.7.2 by @liabru
   http://brm.io/jquery-match-height/
   License MIT */
!(function (t) {
  "use strict";
  "function" == typeof define && define.amd
    ? define(["jquery"], t)
    : "undefined" != typeof module && module.exports
    ? (module.exports = t(require("jquery")))
    : t(jQuery);
})(function (t) {
  var e = -1,
    o = -1,
    n = function (t) {
      return parseFloat(t) || 0;
    },
    a = function (e) {
      var o = 1,
        a = t(e),
        i = null,
        r = [];
      return (
        a.each(function () {
          var e = t(this),
            a = e.offset().top - n(e.css("margin-top")),
            s = r.length > 0 ? r[r.length - 1] : null;
          null === s
            ? r.push(e)
            : Math.floor(Math.abs(i - a)) <= o
            ? (r[r.length - 1] = s.add(e))
            : r.push(e),
            (i = a);
        }),
        r
      );
    },
    i = function (e) {
      var o = {
        byRow: !0,
        property: "height",
        target: null,
        remove: !1,
      };
      return "object" == typeof e
        ? t.extend(o, e)
        : ("boolean" == typeof e
            ? (o.byRow = e)
            : "remove" === e && (o.remove = !0),
          o);
    },
    r = (t.fn.matchHeight = function (e) {
      var o = i(e);
      if (o.remove) {
        var n = this;
        return (
          this.css(o.property, ""),
          t.each(r._groups, function (t, e) {
            e.elements = e.elements.not(n);
          }),
          this
        );
      }
      return this.length <= 1 && !o.target
        ? this
        : (r._groups.push({ elements: this, options: o }),
          r._apply(this, o),
          this);
    });
  (r.version = "0.7.2"),
    (r._groups = []),
    (r._throttle = 80),
    (r._maintainScroll = !1),
    (r._beforeUpdate = null),
    (r._afterUpdate = null),
    (r._rows = a),
    (r._parse = n),
    (r._parseOptions = i),
    (r._apply = function (e, o) {
      var s = i(o),
        h = t(e),
        l = [h],
        c = t(window).scrollTop(),
        p = t("html").outerHeight(!0),
        u = h.parents().filter(":hidden");
      return (
        u.each(function () {
          var e = t(this);
          e.data("style-cache", e.attr("style"));
        }),
        u.css("display", "block"),
        s.byRow &&
          !s.target &&
          (h.each(function () {
            var e = t(this),
              o = e.css("display");
            "inline-block" !== o &&
              "flex" !== o &&
              "inline-flex" !== o &&
              (o = "block"),
              e.data("style-cache", e.attr("style")),
              e.css({
                display: o,
                "padding-top": "0",
                "padding-bottom": "0",
                "margin-top": "0",
                "margin-bottom": "0",
                "border-top-width": "0",
                "border-bottom-width": "0",
                height: "100px",
                overflow: "hidden",
              });
          }),
          (l = a(h)),
          h.each(function () {
            var e = t(this);
            e.attr("style", e.data("style-cache") || "");
          })),
        t.each(l, function (e, o) {
          var a = t(o),
            i = 0;
          if (s.target) i = s.target.outerHeight(!1);
          else {
            if (s.byRow && a.length <= 1) return void a.css(s.property, "");
            a.each(function () {
              var e = t(this),
                o = e.attr("style"),
                n = e.css("display");
              "inline-block" !== n &&
                "flex" !== n &&
                "inline-flex" !== n &&
                (n = "block");
              var a = {
                display: n,
              };
              (a[s.property] = ""),
                e.css(a),
                e.outerHeight(!1) > i && (i = e.outerHeight(!1)),
                o ? e.attr("style", o) : e.css("display", "");
            });
          }
          a.each(function () {
            var e = t(this),
              o = 0;
            (s.target && e.is(s.target)) ||
              ("border-box" !== e.css("box-sizing") &&
                ((o +=
                  n(e.css("border-top-width")) +
                  n(e.css("border-bottom-width"))),
                (o += n(e.css("padding-top")) + n(e.css("padding-bottom")))),
              e.css(s.property, i - o + "px"));
          });
        }),
        u.each(function () {
          var e = t(this);
          e.attr("style", e.data("style-cache") || null);
        }),
        r._maintainScroll &&
          t(window).scrollTop((c / p) * t("html").outerHeight(!0)),
        this
      );
    }),
    (r._applyDataApi = function () {
      var e = {};
      t("[data-match-height], [data-mh]").each(function () {
        var o = t(this),
          n = o.attr("data-mh") || o.attr("data-match-height");
        n in e ? (e[n] = e[n].add(o)) : (e[n] = o);
      }),
        t.each(e, function () {
          this.matchHeight(!0);
        });
    });
  var s = function (e) {
    r._beforeUpdate && r._beforeUpdate(e, r._groups),
      t.each(r._groups, function () {
        r._apply(this.elements, this.options);
      }),
      r._afterUpdate && r._afterUpdate(e, r._groups);
  };
  (r._update = function (n, a) {
    if (a && "resize" === a.type) {
      var i = t(window).width();
      if (i === e) return;
      e = i;
    }
    n
      ? o === -1 &&
        (o = setTimeout(function () {
          s(a), (o = -1);
        }, r._throttle))
      : s(a);
  }),
    t(r._applyDataApi);
  var h = t.fn.on ? "on" : "bind";
  t(window)[h]("load", function (t) {
    r._update(!1, t);
  }),
    t(window)[h]("resize orientationchange", function (t) {
      r._update(!0, t);
    });
});

/////[フッター]ページトップボタン
$(function () {
  var pageTop = $("#page_top");
  pageTop.hide();
  $(window).scroll(function () {
    if ($(this).scrollTop() > 100) {
      //100pxスクロールしたら表示
      pageTop.fadeIn();
    } else {
      pageTop.fadeOut();
    }
  });
  pageTop.click(function () {
    $("body,html").animate(
      {
        scrollTop: 0,
      },
      500
    ); //0.5秒かけてトップへ移動

    pageTop.fadeOut();
    // ボタンをクリックした後、すぐに非表示にする
    return false;
  });

  // フッター手前でストップ
  $("#page_top").hide();
  $(window).on("scroll", function () {
    var scrollHeight = $(document).height();
    var scrollPosition = $(window).height() + $(window).scrollTop();
    var footHeight = $("footer").innerHeight() - 30; // ボタン位置調整
    var footOffsetTop = $("footer").offset().top;

    if ($(window).width() <= 768) {
      if (scrollPosition >= footOffsetTop) {
        $("#page_top").css({
          position: "absolute",
          top: "-20px",
        });
      } else {
        $("#page_top").css({
          position: "fixed",
          bottom: "13px",
          top: "auto",
        });
      }
    } else {
      if (scrollHeight - scrollPosition <= footHeight) {
        $("#page_top").css({
          position: "absolute",
          bottom: footHeight,
        });
      } else {
        $("#page_top").css({
          position: "fixed",
          bottom: "17px",
          top: "auto",
        });
      }
    }
  });
});

/////[商品一覧]FIXボタンエリア
$(function () {
  var fixButton = $(".fix-bottom-contents");
  var footer = $("footer");
  fixButton.hide();

  $(window).scroll(function () {
    var scrollTop = $(this).scrollTop(); // 現在のスクロール位置
    var windowHeight = $(this).height(); // ウィンドウの高さ
    var footerOffset = footer.offset().top; // フッターの位置
    var footHeight = footer.innerHeight(); // フッターの高さ

    // ボタンの表示/非表示とフッター手前でフェードアウトする処理
    if (
      scrollTop > 100 &&
      scrollTop + windowHeight < footerOffset - footHeight
    ) {
      fixButton.fadeIn();
    } else {
      fixButton.fadeOut();
    }
  });
});


/////[TOPページ]多言語対応
document.addEventListener("DOMContentLoaded", () => {
  const switcher = document.getElementById("locale-switcher");

  if (switcher) {
    function localeSwitch(lang) {
      switch (lang) {
        case "en":
          window.location.href = "./index.html";
          break;
        default:
          window.location.href = "./index.html";
      }
    }

    switcher.addEventListener("change", (e) => {
      // option タグの value を読み取る
      const targetLang = e.target.value;
      localeSwitch(targetLang);
    });
  } else {
    console.error("locale-switcher element not found");
  }
});


/////[TOPページ]すべてのチェックボックスにチェックを入れる（テーブルごとに操作可能）
$(function () {
  // すべて選択ボタンのクリックイベント
  $("[id$='-select-all-top'], [id$='-select-all-bottom']").click(function () {
    // idの先頭部分を取得（例: 'first' または 'second'）
    let tablePrefix = $(this).attr("id").split("-select-all")[0];
    // そのテーブルに関連するチェックボックスをすべてチェックする
    $("input[name=" + tablePrefix + "-check-favorite]").prop("checked", true);
  });

  // すべて解除ボタンのクリックイベント
  $("[id$='-clear-all-top'], [id$='-clear-all-bottom']").click(function () {
    // idの先頭部分を取得（例: 'first' または 'second'）
    let tablePrefix = $(this).attr("id").split("-clear-all")[0];
    // そのテーブルに関連するチェックボックスをすべて解除する
    $("input[name=" + tablePrefix + "-check-favorite]").prop("checked", false);
  });
});


/////[TOP]新着商品スライダー
$(function () {
  $(".list-item-gallery.top").slick({
    arrows: true,
    dots: true,
    infinite: true,
    slidesToShow: 4,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 1081, // 1081px以下の場合
        settings: {
          slidesToShow: 3, // 3つのアイテムを表示
        },
      },
      {
        breakpoint: 767, // 767px以下の場合
        settings: {
          slidesToShow: 2, // 2つのアイテムを表示
        },
      },
    ],
  });
});


/////[商品詳細]おすすめスライダー
$(function () {
  $(".list-item-gallery.detail").slick({
    arrows: true,
    dots: false,
    infinite: true,
    slidesToShow: 7,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 1081, // 1081px以下の場合
        settings: {
          slidesToShow: 3, // 3つのアイテムを表示
        },
      },
      {
        breakpoint: 767, // 767px以下の場合
        settings: {
          slidesToShow: 2, // 2つのアイテムを表示
        },
      },
    ],
  });
});


/////[商品詳細]お気に入りボタンクリック
$(function () {
  // .fav-markをすべて取得
  const favMarks = document.querySelectorAll(".fav-mark");

  // 各fav-markに対してクリックイベントを設定
  favMarks.forEach(function (favMark) {
    favMark.addEventListener("click", function () {
      // クリックされたfav-mark内のfav-pctにクラスaddedを追加・削除する
      const favPct = this.querySelector(".fav-pct");
      if (favPct) {
        favPct.classList.toggle("added");
      }
    });
  });
});


/////[商品一覧]表示並べ替えパネルボタン
$(function () {
  // ヘッダー条件検索開閉
  $(".menu_trigger").on("click", function () {
    var filterPanel = $(".sorting_panel");
    if (!filterPanel.hasClass("is-active")) {
      filterPanel.addClass("is-active");
    } else {
      filterPanel.removeClass("is-active");
    }
  });
  // ボタン以外のエリアクリックで閉じる
  $(document).on("click", function (e) {
    if (!$(e.target).closest(".sorting").length) {
      $(".sorting_panel").removeClass("is-active");
    }
  });
  // リストアイテムをクリックした際の動作
  $(".option_item").on("click", function () {
    var selectedText = $(this).text(); // クリックされたリストのテキストを取得
    $(".option_selected").text(selectedText); // 選択されたテキストを表示している要素に反映
    $(".sorting_panel").removeClass("is-active");
  });
});


/////[商品一覧]商品名高さ合わせ
$(document).ready(function () {
  // ページが読み込まれた際に高さを揃える
  equalizeItemNameHeight();
});
function equalizeItemNameHeight() {
  var itemNames = $("li p.item-name");
  var maxHeight = 0;

  itemNames.each(function () {
    var height = $(this).height();
    if (height > maxHeight) {
      maxHeight = height;
    }
  });
  itemNames.height(maxHeight);
}


/////[商品一覧]件数表示切り替え
$(function () {
  // 最初は50件を表示する
  $("#list .item-list li:gt(50)").hide(); // 最初は51個目以降を非表示に

  $(".number-switch .btn").on("click", function () {
    // 50件ボタンがクリックされた時の処理
    if ($(this).text() === "50件") {
      $("#list .item-list li:gt(51)").hide(); // 51個目以降を非表示に
      $("#list .item-list li:lt(50)").show(); // 最初の50個を表示

      // ボタンのスタイルを変更（Activeにする）
      $(".number-switch .btn").removeClass("is-active");
      $(this).addClass("is-active");
    }
    // 100件ボタンがクリックされた時の処理
    else if ($(this).text() === "100件") {
      $("#list .item-list li").show(); // 全てのアイテムを表示

      // ボタンのスタイルを変更（Activeにする）
      $(".number-switch .btn").removeClass("is-active");
      $(this).addClass("is-active");
    }
  });
});

/////[商品一覧]ディスプレイタイプ切り替え
$(function () {
  function toggleActiveClass(button, itemListClass) {
    $(".btn.row, .btn.panel").removeClass("is-active");
    button.addClass("is-active");
    $(".item-list").removeClass("row panel").addClass(itemListClass);
  }

  $(".btn.row").on("click", function () {
    toggleActiveClass($(this), "row");
  });

  $(".btn.panel").on("click", function () {
    toggleActiveClass($(this), "panel");
  });
});


/////[商品一覧]もっと見るボタン
$(function () {
  /* 初期表示リスト数、以降表示ブロック単位 */
  var moreNum = 20;

  /* 各リストごとに処理を行う */
  $(".auction-contents").each(function () {
    var $this = $(this);

    /* 初期表示以降のリストを非表示 */
    $this
      .find(".list-item-table table tr:nth-child(n + " + (moreNum + 1) + ")")
      .addClass("is-hidden");

    /* 全リスト表示後ボタンフェードアウト */
    $this.find(".list-more button").on("click", function () {
      $this
        .find(".list-item-table table tr.is-hidden")
        .slice(0, moreNum)
        .removeClass("is-hidden");

      if ($this.find(".list-item-table table tr.is-hidden").length == 0) {
        $this.find(".list-more").fadeOut();
      }
    });

    /* リスト数が初期表示数以下の場合はボタンを非表示 */
    var list = $this.find(".list-item-table table tr").length;
    if (list < moreNum) {
      $this.find(".list-more").addClass("is-hidden");
    }
  });
});


/////[TOP]お知らせもっと見るボタン
$(function () {
  // 初期表示リスト数、以降表示ブロック単位
  var moreNum = 3;
  // 各リストごとに処理を行う ※記述必須
  $(".info-box-notice").each(function () {
    var $this = $(this);
    // 初期表示以降のリストを非表示
    $this
      .find(".info-item li:nth-child(n + " + (moreNum + 1) + ")")
      .addClass("is-hidden");
    // すべてのリストが表示されたらボタンをフェードアウト
    $this.find(".newslist-more .btn").on("click", function () {
      $this
        .find(".info-item li.is-hidden")
        .slice(0, moreNum)
        .removeClass("is-hidden");

      if ($this.find(".info-item li.is-hidden").length == 0) {
        $(this).fadeOut();
      }
    });
    // 初期リスト数以下の場合はボタンを非表示
    if ($this.find(".info-item li").length <= moreNum) {
      $this.find(".newslist-more .btn").hide();
    }
  });
});

/////[商品詳細]モーダル（入札）
$(function () {
  var open = $(".modal-open"),
    close = $(".modal-close, .button-modal-close"),
    container = $(".modal-container");
  open.on("click", function () {
    container.addClass("active");
    $("body").css("overflow", "hidden"); // モーダルが開くときにbodyのスクロールを禁止
    return false;
  });
  close.on("click", function () {
    container.removeClass("active");
  });
  $(document).on("click", function (e) {
    if (!$(e.target).closest(".modal-body").length) {
      container.removeClass("active");
    }
  });
});



/////チェック時のボタン活性・非活性
$(function() {
  $(function() {
    $('#sbm-login').attr('disabled', 'disabled');
    $('#rule-chk').click(function() {
      if ( $(this).prop('checked') == false ) {
        $('#sbm-login').attr('disabled', 'disabled');
      } else {
        $('#sbm-login').removeAttr('disabled');
      }
    });
  });
});

/////[入札確認]スクロール完了とチェック時にボタン活性化




/////[商品一覧]モーダル（ブランド選択）
$(function () {
  var open = $(".modal-brand-open"),
    close = $(".modal-close"),
    container = $(".modal-brand-container"),
    conditions = $(".search-conditions"); // `.conditions` セレクタの追加

  open.on("click", function () {
    container.addClass("active");
    $("body").css("overflow", "hidden"); // モーダルが開くときにbodyのスクロールを禁止
    return false;
  });
  var closeModal = function () {
    container.removeClass("active");
    $("body").css("overflow", ""); // モーダルが閉じるときにbodyのスクロールを許可
  };
  close.on("click", closeModal);
  conditions.on("click", closeModal);
  $(document).on("click", function (e) {
    if (!$(e.target).closest(".modal-body").length) {
      closeModal();
    }
  });
});

/////[お問い合わせチャット]タブ切り替え
$(function () {
  function toggleTab(button, itemListClass) {
    $(".chat-item, .chat-shipping").hide();
    $(itemListClass).show();
  }

  $(".btn.item, .btn.shipping").on("click", function () {
    $(".btn.item, .btn.shipping").removeClass("is-active");
    $(this).addClass("is-active");

    if ($(this).hasClass("item")) {
      toggleTab($(this), ".chat-item");
    } else if ($(this).hasClass("shipping")) {
      toggleTab($(this), ".chat-shipping");
    }
  });
});


/////FAQ アコーディオン
$(function () {
  let accordionDetails = ".js-details";
  let accordionSummary = ".js-details-summary";
  let accordionContent = ".js-details-content";
  let speed = 300;

  $(accordionSummary).each(function () {
    $(this).on("click", function (event) {
      // デフォルトの挙動を無効化
      event.preventDefault();
      // summaryにis-activeクラスを切り替え
      $(this).toggleClass("is-active");

      if ($(this).parent($(accordionDetails)).attr("open")) {
        // アコーディオンを閉じるときの処理
        $(this)
          .nextAll($(accordionContent))
          .slideUp(speed, function () {
            // アニメーションの完了後にopen属性を取り除く
            $(this).parent($(accordionDetails)).removeAttr("open");
          });
      } else {
        // アコーディオンを開くときの処理
        // open属性を付ける
        $(this).parent($(accordionDetails)).attr("open", "true");
        // いったんdisplay:none;してからslideDownで開く
        $(this).nextAll($(accordionContent)).hide().slideDown(speed);
      }
    });
  });
});
