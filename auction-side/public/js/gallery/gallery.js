(() => {
  //slickスライダー初期化
  $(".slider-for").slick({
    asNavFor: ".slider-nav",
    autoplay: false,
    arrows: true,
    infinite: true /*必須*/,
  });
  $(".slider-nav").slick({
    slidesToShow: 7,
    slidesToScroll: 1,
    asNavFor: ".slider-for",
    focusOnSelect: true,
    autoplay: false,
    arrows: true,
    responsive: [
      {
        breakpoint: 767,
        settings: {
          slidesToShow: 4,
        },
      },
    ],
  });
})();

(() => {
  const $gallery = document.querySelector(".my-gallery");
  const $pswpElement = document.querySelector(".pswp");

  if (!$gallery || !$pswpElement) return;

  const items = Array.prototype.map.call(
    $gallery.querySelectorAll("figure:not(.slick-cloned)"),
    ($slide) => {
      const $a = $slide.querySelector("a");
      const size = $a.getAttribute("data-size").split("x");
      return {
        src: $a.getAttribute("href"),
        w: parseInt(size[0], 10),
        h: parseInt(size[1], 10),
        el: $slide,
      };
    }
  );

  const openPhotoSwipe = (index) => {
    const options = {
      index,
      history: false,
      getThumbBoundsFn(index) {
        // See Options -> getThumbBoundsFn section of documentation for more info
        const thumbnail = items[index].el.querySelector("img"); // find thumbnail
        const pageYScroll =
          window.pageYOffset || document.documentElement.scrollTop;
        const elRect = thumbnail.getBoundingClientRect();

        return {
          x: elRect.left,
          y: elRect.top + pageYScroll,
          w: elRect.width,
        };
      },
    };

    const gallery = new PhotoSwipe(
      $pswpElement,
      PhotoSwipeUI_Default,
      items,
      options
    );
    gallery.init();
  };

  $gallery.querySelector(".slick-list").addEventListener("click", (event) => {
    event.preventDefault();
    const index = $(".slider-for").slick("slickCurrentSlide");
    openPhotoSwipe(index);
  });
})();
